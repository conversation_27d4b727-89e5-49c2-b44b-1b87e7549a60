import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNumber, ArrayMinSize, ArrayMaxSize } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc xóa nhiều sản phẩm khách hàng cùng lúc
 */
export class BulkDeleteCustomerProductDto {
  @ApiProperty({
    description: 'Danh sách ID sản phẩm cần xóa',
    type: [Number],
    example: [123, 124, 125],
    minItems: 1,
    maxItems: 50,
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 sản phẩm để xóa' })
  @ArrayMaxSize(50, { message: 'Chỉ có thể xóa tối đa 50 sản phẩm cùng lúc' })
  @Type(() => Number)
  @IsNumber({}, { each: true, message: 'ID sản phẩm phải là số' })
  productIds: number[];
}
