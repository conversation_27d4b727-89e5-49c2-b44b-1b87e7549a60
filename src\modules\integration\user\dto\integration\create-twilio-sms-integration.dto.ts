import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject, IsString } from 'class-validator';

/**
 * DTO cho việc tạo mới tích hợp Twilio SMS
 */
export class CreateTwilioSmsIntegrationDto {
  /**
   * Tên của tích hợp Twilio SMS
   * @example "Google Analytics Integration"
   */
  @ApiProperty({
    description: 'Tên của tích hợp Twilio SMS',
    example: 'Google Analytics Integration',
    required: true
  })
  @IsNotEmpty({ message: 'Tên tích hợp không được để trống' })
  @IsString({ message: 'Tên tích hợp phải là chuỗi' })
  integrationName: string;

  /**
   * Thông tin cấu hình Twilio SMS
   * @example { "TWILIO_AUTH_TOKEN": "GA-12345", "TWILIO_BASE_DOMAIN": "123456789" }
   */
  @ApiProperty({
    description: 'Thông tin cấu hình Twilio SMS',
    example: { 
      TWILIO_AUTH_TOKEN: 'GA-12345-snfanf-uuia',
      TWILIO_BASE_DOMAIN: 'tahhhdz.com'
    },
    required: true
  })
  @IsNotEmpty({ message: 'Thông tin cấu hình không được để trống' })
  @IsObject({ message: 'Thông tin cấu hình phải là đối tượng JSON' })
  info: {
    TWILIO_AUTH_TOKEN: string;
    TWILIO_BASE_DOMAIN: string;
  };
}
