import { ConvertConfig } from '@modules/agent/interfaces/convert-config.interface';
import {
  ConvertConfigItemDto,
  ConversionResponseDto
} from '../dto/conversion';

/**
 * Mapper cho Conversion operations
 */
export class ConversionMapper {
  /**
   * <PERSON>yển đổi ConvertConfig array sang ConversionResponseDto
   * @param convertConfig Array ConvertConfig từ entity
   * @returns ConversionResponseDto
   */
  static toResponseDto(convertConfig: ConvertConfig[]): ConversionResponseDto {
    // Đảm bảo convertConfig là array, defensive programming
    const safeConvertConfig = Array.isArray(convertConfig) ? convertConfig : [];

    // Chuyển đổi từng item sang DTO
    const convertConfigItems: ConvertConfigItemDto[] = safeConvertConfig.map(item => ({
      name: item.name,
      type: item.type,
      description: item.description,
      required: item.required,
      defaultValue: item.defaultValue,
    }));

    // Tính toán thống kê
    const totalFields = convertConfigItems.length;
    const requiredFields = convertConfigItems.filter(item => item.required).length;

    return {
      convertConfig: convertConfigItems,
      totalFields,
      requiredFields,
      updatedAt: Date.now(), // Sử dụng timestamp hiện tại
    };
  }

  /**
   * Chuyển đổi ConvertConfigItemDto array sang ConvertConfig array
   * @param convertConfigItems Array ConvertConfigItemDto từ DTO
   * @returns ConvertConfig array
   */
  static fromDtoToEntity(convertConfigItems: ConvertConfigItemDto[]): ConvertConfig[] {
    return convertConfigItems.map(item => ({
      name: item.name,
      type: item.type,
      description: item.description,
      required: item.required,
      defaultValue: item.defaultValue,
    }));
  }

  /**
   * Chuyển đổi ConvertConfig array sang ConvertConfigItemDto array
   * @param convertConfig Array ConvertConfig từ entity
   * @returns ConvertConfigItemDto array
   */
  static toConfigItemDtos(convertConfig: ConvertConfig[]): ConvertConfigItemDto[] {
    return convertConfig.map(item => ({
      name: item.name,
      type: item.type,
      description: item.description,
      required: item.required,
      defaultValue: item.defaultValue,
    }));
  }

  /**
   * Validate conversion config structure
   * @param convertConfig Array ConvertConfig để validate
   * @returns boolean
   */
  static validateConvertConfig(convertConfig: ConvertConfig[]): boolean {
    if (!Array.isArray(convertConfig)) {
      return false;
    }

    // Kiểm tra duplicate names
    const names = convertConfig.map(item => item.name);
    const uniqueNames = new Set(names);
    if (names.length !== uniqueNames.size) {
      return false;
    }

    // Validate từng item
    for (const item of convertConfig) {
      if (!item.name || typeof item.name !== 'string') {
        return false;
      }

      if (!item.type || !['string', 'number', 'boolean', 'array', 'object'].includes(item.type)) {
        return false;
      }
    }

    return true;
  }

  /**
   * Tạo conversion config mặc định cho agent mới
   * @returns ConvertConfig array mặc định
   */
  static createDefaultConvertConfig(): ConvertConfig[] {
    return [
      {
        name: 'customer_name',
        type: 'string',
        description: 'Tên đầy đủ của khách hàng',
        required: true,
        defaultValue: ''
      },
      {
        name: 'customer_email',
        type: 'string',
        description: 'Email của khách hàng',
        required: true,
        defaultValue: ''
      },
      {
        name: 'customer_phone',
        type: 'string',
        description: 'Số điện thoại của khách hàng',
        required: false,
        defaultValue: ''
      }
    ];
  }
}
