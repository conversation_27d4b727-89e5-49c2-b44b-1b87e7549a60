import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { In } from 'typeorm';
import { IntegrationRepository } from '../../repositories';
import { Integration } from '../../entities';
import { OwnedTypeEnum } from '../../enums';
import {
  CreateSmsServerDto,
  SmsServerResponseDto,
  UpdateSmsServerDto,
} from '../dto/sms';
import { SmsProviderType } from '../../interfaces/sms-provider-config.interface';
import { EncryptionService } from '@shared/services/encryption.service';

@Injectable()
export class SmsServerConfigurationUserService {
  private readonly logger = new Logger(SmsServerConfigurationUserService.name);

  constructor(
    private readonly integrationRepository: IntegrationRepository,
    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * Tạo mới cấu hình máy chủ SMS
   */
  async create(
    createDto: CreateSmsServerDto,
    userId: number,
  ): Promise<SmsServerResponseDto> {
    try {
      // Validate integrationName và lấy type SMS
      const smsType = this.validateAndGetSmsType(createDto.info);
      const providerName = createDto.info.integrationName;

      this.logger.log(
        `Tạo cấu hình SMS mới cho user ${userId}, provider: ${providerName}`,
      );

      // Validate cấu hình theo từng provider (trước khi mã hóa)
      await this.validateProviderConfig(providerName as SmsProviderType, createDto.info);

      // Tạo integration mới cho SMS
      const integration = new Integration();
      integration.userId = userId; // Lấy từ user đăng nhập
      integration.integrationName = createDto.integration_name; // Tên do user đặt
      integration.type = smsType; // Luôn là 'SMS'
      integration.ownedType = OwnedTypeEnum.USER; // Tự động set USER

      // Mã hóa toàn bộ info object
      integration.info = this.encryptSensitiveSettings(createDto.info);

      const savedIntegration =
        await this.integrationRepository.save(integration);

      return this.toResponseDto(savedIntegration);
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo cấu hình SMS: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật cấu hình máy chủ SMS
   */
  async update(
    id: number,
    updateDto: UpdateSmsServerDto,
    userId: number,
  ): Promise<SmsServerResponseDto> {
    try {
      this.logger.log(`Cập nhật cấu hình SMS ${id} cho user ${userId}`);

      const smsProviderTypes = Object.values(SmsProviderType);

      const existingIntegration = await this.integrationRepository.findOne({
        where: {
          id,
          userId,
          type: In(smsProviderTypes),
        },
      });

      if (!existingIntegration) {
        throw new NotFoundException('Không tìm thấy cấu hình SMS');
      }

      // Cập nhật integration name nếu có
      if (updateDto.integration_name) {
        existingIntegration.integrationName = updateDto.integration_name;
      }

      // Cập nhật info nếu có
      if (updateDto.info) {
        // Validate integrationName và lấy type SMS
        const smsType = this.validateAndGetSmsType(updateDto.info);
        const providerName = updateDto.info.integrationName;

        // Cập nhật type tự động
        existingIntegration.type = smsType;

        // Validate cấu hình nếu có thay đổi (trước khi mã hóa)
        await this.validateProviderConfig(providerName as SmsProviderType, updateDto.info);

        // Mã hóa toàn bộ info object mới
        existingIntegration.info = this.encryptSensitiveSettings(
          updateDto.info,
        );
      }

      const updatedIntegration =
        await this.integrationRepository.save(existingIntegration);

      return this.toResponseDto(updatedIntegration);
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật cấu hình SMS: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy danh sách cấu hình SMS của user
   */
  async findAll(userId: number): Promise<SmsServerResponseDto[]> {
    try {
      const integrations = await this.integrationRepository.find({
        where: {
          userId,
          type: 'SMS',
        },
      });

      return integrations.map((integration) => this.toResponseDto(integration));
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách cấu hình SMS: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy cấu hình SMS theo ID
   */
  async findOne(id: number, userId: number): Promise<SmsServerResponseDto> {
    try {
      const integration = await this.integrationRepository.findOne({
        where: {
          id,
          userId,
          type: 'SMS',
        },
      });

      if (!integration) {
        throw new NotFoundException('Không tìm thấy cấu hình SMS');
      }

      return this.toResponseDto(integration);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy cấu hình SMS: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xóa cấu hình SMS
   */
  async remove(id: number, userId: number): Promise<void> {
    try {
      const integration = await this.integrationRepository.findOne({
        where: {
          id,
          userId,
          type: 'SMS',
        },
      });

      if (!integration) {
        throw new NotFoundException('Không tìm thấy cấu hình SMS');
      }

      await this.integrationRepository.remove(integration);
      this.logger.log(`Đã xóa cấu hình SMS ${id} của user ${userId}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa cấu hình SMS: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Validate cấu hình theo provider
   */
  private async validateProviderConfig(
    providerName: SmsProviderType,
    config: any,
  ): Promise<void> {
    switch (providerName) {
      case SmsProviderType.FPT_SMS:
        this.validateFptSmsConfig(config);
        break;

      default:
        throw new BadRequestException(
          `Provider ${providerName} chưa được hỗ trợ`,
        );
    }
  }

  /**
   * Validate cấu hình FPT SMS
   */
  private validateFptSmsConfig(config: any): void {
    // Kiểm tra clientId trực tiếp trong config
    const clientId = config.clientId;
    if (!clientId) {
      throw new BadRequestException('FPT SMS yêu cầu clientId trong info');
    }
  }

  /**
   * Validate integrationName và trả về type là 'SMS'
   */
  private validateAndGetSmsType(info: any): string {
    if (!info || !info.integrationName) {
      throw new BadRequestException('Thiếu integrationName trong info');
    }

    const integrationName = info.integrationName.toUpperCase();

    // Validate integrationName có hợp lệ không (dựa trên SmsProviderType enum)
    const validProviders = Object.values(SmsProviderType);
    if (!validProviders.includes(integrationName as SmsProviderType)) {
      throw new BadRequestException(`Provider không được hỗ trợ: ${integrationName}. Các provider hỗ trợ: ${validProviders.join(', ')}`);
    }

    // Luôn trả về 'SMS' cho type
    return 'SMS';
  }

  /**
   * Lấy cấu hình SMS đã giải mã để sử dụng cho gửi SMS
   * @param userId ID của người dùng
   * @param providerType
   * @returns Cấu hình SMS đã giải mã
   */
  async getSmsConfigForSending(
    userId: number,
    providerType?: SmsProviderType,
  ): Promise<any> {
    try {
      const whereCondition: any = { userId };
      if (providerType) {
        whereCondition.type = providerType;
      }

      const integration = await this.integrationRepository.findOne({
        where: whereCondition,
      });

      if (!integration) {
        throw new NotFoundException('Không tìm thấy cấu hình SMS');
      }

      const info = integration.info || {};

      // Giải mã toàn bộ info object
      return this.decryptSensitiveSettings(info);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy cấu hình SMS cho gửi tin: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Kiểm tra xem user có cấu hình SMS nào không
   * @param userId ID của người dùng
   * @returns true nếu có cấu hình SMS
   */
  async hasSmsConfig(userId: number): Promise<boolean> {
    try {
      const count = await this.integrationRepository.count({
        where: {
          userId,
          type: 'SMS'
        },
      });
      return count > 0;
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra cấu hình SMS: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Chuyển đổi entity sang response DTO
   */
  private toResponseDto(integration: Integration): SmsServerResponseDto {
    const info = integration.info || {};

    // Giải mã và ẩn thông tin nhạy cảm từ info
    const decryptedInfo = this.decryptSensitiveSettings(info);
    const sanitizedInfo = this.sanitizeSettings(decryptedInfo);

    return {
      id: integration.id,
      integration_name: integration.integrationName,
      type: integration.type,
      user_id: integration.userId,
      info: sanitizedInfo,
      owned_type: integration.ownedType,
      created_at: integration.createdAt.toISOString(),
    };
  }

  /**
   * Ẩn thông tin nhạy cảm trong settings
   */
  private sanitizeSettings(settings: any): any {
    if (!settings) return settings;

    const sanitized = { ...settings };

    // Ẩn các trường nhạy cảm ở level đầu
    if (sanitized.apiKey) {
      sanitized.apiKey = this.maskSensitiveData(sanitized.apiKey);
    }
    if (sanitized.clientId) {
      sanitized.clientId = this.maskSensitiveData(sanitized.clientId);
    }
    if (sanitized.authToken) {
      sanitized.authToken = this.maskSensitiveData(sanitized.authToken);
    }
    if (sanitized.accountSid) {
      sanitized.accountSid = this.maskSensitiveData(sanitized.accountSid);
    }
    if (sanitized.apiToken) {
      sanitized.apiToken = this.maskSensitiveData(sanitized.apiToken);
    }
    if (sanitized.apiSecret) {
      sanitized.apiSecret = this.maskSensitiveData(sanitized.apiSecret);
    }

    // Ẩn các trường nhạy cảm trong additionalSettings
    if (
      sanitized.additionalSettings &&
      typeof sanitized.additionalSettings === 'object'
    ) {
      const sanitizedAdditional = { ...sanitized.additionalSettings };

      if (sanitizedAdditional.clientId) {
        sanitizedAdditional.clientId = this.maskSensitiveData(
          sanitizedAdditional.clientId,
        );
      }
      if (sanitizedAdditional.apiKey) {
        sanitizedAdditional.apiKey = this.maskSensitiveData(
          sanitizedAdditional.apiKey,
        );
      }
      if (sanitizedAdditional.authToken) {
        sanitizedAdditional.authToken = this.maskSensitiveData(
          sanitizedAdditional.authToken,
        );
      }
      if (sanitizedAdditional.accountSid) {
        sanitizedAdditional.accountSid = this.maskSensitiveData(
          sanitizedAdditional.accountSid,
        );
      }
      if (sanitizedAdditional.apiToken) {
        sanitizedAdditional.apiToken = this.maskSensitiveData(
          sanitizedAdditional.apiToken,
        );
      }
      if (sanitizedAdditional.apiSecret) {
        sanitizedAdditional.apiSecret = this.maskSensitiveData(
          sanitizedAdditional.apiSecret,
        );
      }

      sanitized.additionalSettings = sanitizedAdditional;
    }

    return sanitized;
  }

  /**
   * Ẩn dữ liệu nhạy cảm
   */
  private maskSensitiveData(data: string): string {
    if (!data || data.length <= 6) return '***';
    return data.substring(0, 3) + '***' + data.substring(data.length - 3);
  }

  /**
   * Mã hóa các trường nhạy cảm trong settings
   */
  private encryptSensitiveSettings(settings: any): any {
    if (!settings) return settings;

    const encrypted = { ...settings };

    // Mã hóa các trường nhạy cảm ở level đầu
    if (encrypted.apiKey) {
      encrypted.apiKey = this.encryptionService.encrypt(encrypted.apiKey);
    }
    if (encrypted.clientId) {
      encrypted.clientId = this.encryptionService.encrypt(encrypted.clientId);
    }
    if (encrypted.authToken) {
      encrypted.authToken = this.encryptionService.encrypt(encrypted.authToken);
    }
    if (encrypted.accountSid) {
      encrypted.accountSid = this.encryptionService.encrypt(
        encrypted.accountSid,
      );
    }
    if (encrypted.apiToken) {
      encrypted.apiToken = this.encryptionService.encrypt(encrypted.apiToken);
    }
    if (encrypted.apiSecret) {
      encrypted.apiSecret = this.encryptionService.encrypt(encrypted.apiSecret);
    }

    // Mã hóa các trường nhạy cảm trong additionalSettings
    if (
      encrypted.additionalSettings &&
      typeof encrypted.additionalSettings === 'object'
    ) {
      const encryptedAdditional = { ...encrypted.additionalSettings };

      if (encryptedAdditional.clientId) {
        encryptedAdditional.clientId = this.encryptionService.encrypt(
          encryptedAdditional.clientId,
        );
      }
      if (encryptedAdditional.apiKey) {
        encryptedAdditional.apiKey = this.encryptionService.encrypt(
          encryptedAdditional.apiKey,
        );
      }
      if (encryptedAdditional.authToken) {
        encryptedAdditional.authToken = this.encryptionService.encrypt(
          encryptedAdditional.authToken,
        );
      }
      if (encryptedAdditional.accountSid) {
        encryptedAdditional.accountSid = this.encryptionService.encrypt(
          encryptedAdditional.accountSid,
        );
      }
      if (encryptedAdditional.apiToken) {
        encryptedAdditional.apiToken = this.encryptionService.encrypt(
          encryptedAdditional.apiToken,
        );
      }
      if (encryptedAdditional.apiSecret) {
        encryptedAdditional.apiSecret = this.encryptionService.encrypt(
          encryptedAdditional.apiSecret,
        );
      }

      encrypted.additionalSettings = encryptedAdditional;
    }

    return encrypted;
  }

  /**
   * Giải mã các trường nhạy cảm trong settings
   */
  private decryptSensitiveSettings(settings: any): any {
    if (!settings) return settings;

    const decrypted = { ...settings };

    try {
      // Giải mã các trường nhạy cảm ở level đầu
      if (decrypted.apiKey) {
        decrypted.apiKey = this.safeDecrypt(decrypted.apiKey);
      }
      if (decrypted.clientId) {
        decrypted.clientId = this.safeDecrypt(decrypted.clientId);
      }
      if (decrypted.authToken) {
        decrypted.authToken = this.safeDecrypt(decrypted.authToken);
      }
      if (decrypted.accountSid) {
        decrypted.accountSid = this.safeDecrypt(decrypted.accountSid);
      }
      if (decrypted.apiToken) {
        decrypted.apiToken = this.safeDecrypt(decrypted.apiToken);
      }
      if (decrypted.apiSecret) {
        decrypted.apiSecret = this.safeDecrypt(decrypted.apiSecret);
      }

      // Giải mã các trường nhạy cảm trong additionalSettings
      if (
        decrypted.additionalSettings &&
        typeof decrypted.additionalSettings === 'object'
      ) {
        const decryptedAdditional = { ...decrypted.additionalSettings };

        if (decryptedAdditional.clientId) {
          decryptedAdditional.clientId = this.safeDecrypt(
            decryptedAdditional.clientId,
          );
        }
        if (decryptedAdditional.apiKey) {
          decryptedAdditional.apiKey = this.safeDecrypt(
            decryptedAdditional.apiKey,
          );
        }
        if (decryptedAdditional.authToken) {
          decryptedAdditional.authToken = this.safeDecrypt(
            decryptedAdditional.authToken,
          );
        }
        if (decryptedAdditional.accountSid) {
          decryptedAdditional.accountSid = this.safeDecrypt(
            decryptedAdditional.accountSid,
          );
        }
        if (decryptedAdditional.apiToken) {
          decryptedAdditional.apiToken = this.safeDecrypt(
            decryptedAdditional.apiToken,
          );
        }
        if (decryptedAdditional.apiSecret) {
          decryptedAdditional.apiSecret = this.safeDecrypt(
            decryptedAdditional.apiSecret,
          );
        }

        decrypted.additionalSettings = decryptedAdditional;
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi giải mã settings: ${error.message}`,
        error.stack,
      );
      // Không throw error, trả về dữ liệu gốc
      return settings;
    }

    return decrypted;
  }

  /**
   * Giải mã an toàn - nếu không giải mã được thì trả về giá trị gốc
   */
  private safeDecrypt(value: string): string {
    if (!value) return value;

    try {
      // Kiểm tra xem có phải dữ liệu đã mã hóa không
      // Dữ liệu mã hóa thường có độ dài và format đặc biệt
      if (this.isEncryptedData(value)) {
        return this.encryptionService.decrypt(value);
      } else {
        // Nếu không phải dữ liệu mã hóa, trả về giá trị gốc
        return value;
      }
    } catch (error) {
      this.logger.warn(
        `Không thể giải mã giá trị, sử dụng giá trị gốc: ${error.message}`,
      );
      return value;
    }
  }

  /**
   * Kiểm tra xem có phải dữ liệu đã mã hóa không
   */
  private isEncryptedData(value: string): boolean {
    if (!value || typeof value !== 'string') return false;

    // Dữ liệu mã hóa thường có độ dài lớn hơn và chứa ký tự đặc biệt
    // Điều chỉnh logic này dựa trên cách mã hóa của EncryptionService
    return value.length > 50 && /^[A-Za-z0-9+/=]+$/.test(value);
  }
}
