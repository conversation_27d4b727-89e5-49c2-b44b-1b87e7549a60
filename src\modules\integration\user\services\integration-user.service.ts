import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions';
import { ErrorCode } from '@common/exceptions';
import { IntegrationRepository } from '@modules/integration/repositories';
import { Integration } from '@modules/integration/entities';
import { OwnedTypeEnum } from '@modules/integration/enums';
import { INTEGRATION_ERROR_CODES } from '@modules/integration/exceptions/integration-error.code';
import { PaginatedResult } from '@common/response';
import {
  CreateIntegrationDto,
  CreateTwilioSmsIntegrationDto,
  UpdateIntegrationDto,
  IntegrationResponseDto,
  QueryIntegrationDto,
} from '../dto/integration';

/**
 * Service xử lý các thao tác liên quan đến tích hợp của người dùng
 */
@Injectable()
export class IntegrationUserService {
  private readonly logger = new Logger(IntegrationUserService.name);

  constructor(private readonly integrationRepository: IntegrationRepository) {}

  /**
   * Tạo mới tích hợp
   * @param createDto DTO chứa thông tin tạo mới
   * @param userId ID của người dùng
   * @returns Thông tin tích hợp đã tạo
   */
  @Transactional()
  async createIntegration(
    createDto: CreateIntegrationDto,
    userId: number,
  ): Promise<IntegrationResponseDto> {
    try {
      // Tạo đối tượng Integration mới
      const integration = new Integration();
      integration.integrationName = createDto.integrationName;
      integration.type = createDto.type;
      integration.userId = userId;
      integration.info = createDto.info || null;
      integration.ownedType = OwnedTypeEnum.USER;

      // Lưu vào database
      const savedIntegration = await this.integrationRepository.save(integration);

      // Chuyển đổi và trả về DTO
      return plainToInstance(IntegrationResponseDto, savedIntegration, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tạo tích hợp: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.INTEGRATION_CREATE_FAILED,
        `Lỗi khi tạo tích hợp: ${error.message}`,
      );
    }
  }

  /**
   * Tạo mới tích hợp Twilio SMS
   * @param createDto DTO chứa thông tin tạo mới tích hợp Twilio SMS
   * @param userId ID của người dùng
   * @returns Thông tin tích hợp Twilio SMS đã tạo
   */
  @Transactional()
  async createTwilioSmsIntegration(
    createDto: CreateTwilioSmsIntegrationDto,
    userId: number,
  ): Promise<IntegrationResponseDto> {
    try {
      // Tạo đối tượng Integration mới cho Twilio SMS
      const integration = new Integration();
      integration.integrationName = createDto.integrationName;
      integration.type = 'SMS TWILIO'; // Loại tích hợp cố định cho Twilio SMS
      integration.userId = userId;
      integration.info = createDto.info;
      integration.ownedType = OwnedTypeEnum.USER;

      // Lưu vào database
      const savedIntegration = await this.integrationRepository.save(integration);

      // Chuyển đổi và trả về DTO
      return plainToInstance(IntegrationResponseDto, savedIntegration, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tạo tích hợp Twilio SMS: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.INTEGRATION_CREATE_FAILED,
        `Lỗi khi tạo tích hợp Twilio SMS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách tích hợp
   * @param queryDto DTO chứa tham số truy vấn
   * @param userId ID của người dùng
   * @returns Danh sách tích hợp với phân trang
   */
  async getIntegrations(
    queryDto: QueryIntegrationDto,
    userId: number,
  ): Promise<PaginatedResult<IntegrationResponseDto>> {
    try {
      // Lấy danh sách tích hợp từ repository
      const queryParams = {
        ...queryDto,
        userId,
      };

      const result = await this.integrationRepository.findAll(queryParams);

      // Chuyển đổi các phần tử sang DTO
      const items = result.items.map(integration =>
        plainToInstance(IntegrationResponseDto, integration, {
          excludeExtraneousValues: true,
        }),
      );

      // Trả về kết quả với phân trang
      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách tích hợp: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.INTEGRATION_LIST_FAILED,
        `Lỗi khi lấy danh sách tích hợp: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin tích hợp theo ID
   * @param id ID của tích hợp
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết của tích hợp
   */
  async getIntegrationById(
    id: number,
    userId: number,
  ): Promise<IntegrationResponseDto> {
    try {
      // Lấy thông tin tích hợp từ repository
      const integration = await this.integrationRepository.findById(id, userId);
      
      // Kiểm tra tích hợp tồn tại
      if (!integration) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND,
          `Không tìm thấy tích hợp với ID ${id}`,
        );
      }

      // Chuyển đổi và trả về DTO
      return plainToInstance(IntegrationResponseDto, integration, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      
      this.logger.error(`Lỗi khi lấy thông tin tích hợp: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.INTEGRATION_LIST_FAILED,
        `Lỗi khi lấy thông tin tích hợp: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin tích hợp
   * @param id ID của tích hợp
   * @param updateDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng
   * @returns Thông tin tích hợp đã cập nhật
   */
  @Transactional()
  async updateIntegration(
    id: number,
    updateDto: UpdateIntegrationDto,
    userId: number,
  ): Promise<IntegrationResponseDto> {
    try {
      // Lấy thông tin tích hợp hiện tại
      const integration = await this.integrationRepository.findById(id, userId);
      
      // Kiểm tra tích hợp tồn tại
      if (!integration) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND,
          `Không tìm thấy tích hợp với ID ${id}`,
        );
      }

      // Cập nhật thông tin
      if (updateDto.integrationName !== undefined) {
        integration.integrationName = updateDto.integrationName;
      }
      
      if (updateDto.type !== undefined) {
        integration.type = updateDto.type;
      }
      
      if (updateDto.info !== undefined) {
        integration.info = updateDto.info;
      }

      // Lưu vào database
      const updatedIntegration = await this.integrationRepository.save(integration);

      // Chuyển đổi và trả về DTO
      return plainToInstance(IntegrationResponseDto, updatedIntegration, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      
      this.logger.error(`Lỗi khi cập nhật tích hợp: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.INTEGRATION_UPDATE_FAILED,
        `Lỗi khi cập nhật tích hợp: ${error.message}`,
      );
    }
  }

  /**
   * Xóa tích hợp
   * @param id ID của tích hợp
   * @param userId ID của người dùng
   * @returns Kết quả xóa
   */
  @Transactional()
  async deleteIntegration(
    id: number,
    userId: number,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Lấy thông tin tích hợp hiện tại
      const integration = await this.integrationRepository.findById(id, userId);
      
      // Kiểm tra tích hợp tồn tại
      if (!integration) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND,
          `Không tìm thấy tích hợp với ID ${id}`,
        );
      }

      // Xóa tích hợp
      await this.integrationRepository.delete(id);

      return {
        success: true,
        message: 'Xóa tích hợp thành công',
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      
      this.logger.error(`Lỗi khi xóa tích hợp: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.INTEGRATION_DELETE_FAILED,
        `Lỗi khi xóa tích hợp: ${error.message}`,
      );
    }
  }
} 