import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ProductPrice, ShipmentConfigType } from '@modules/business/interfaces';

/**
 * Entity đại diện cho bảng physical_product_variants trong cơ sở dữ liệu
 * Các biến thể của sản phẩm vật lý
 */
@Entity('physical_product_variants')
export class PhysicalProductVariant {
  /**
   * ID tự tăng của biến thể
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID sản phẩm vật lý cha
   */
  @Column({
    name: 'physical_product_id',
    type: 'bigint',
    nullable: true,
    comment: 'ID sản phẩm vật lý cha',
  })
  physicalProductId: number | null;

  /**
   * Tên biến thể
   */
  @Column({
    name: 'name',
    length: 255,
    nullable: false,
    comment: 'Tên biến thể',
  })
  name: string;

  /**
   * <PERSON><PERSON> tả biến thể
   */
  @Column({
    name: 'description',
    length: 500,
    nullable: true,
    comment: 'Mô tả biến thể',
  })
  description: string | null;

  /**
   * Mã SKU riêng của biến thể
   */
  @Column({
    name: 'sku',
    length: 100,
    nullable: true,
    comment: 'Mã SKU riêng của biến thể',
  })
  sku: string | null;

  /**
   * Mã vạch riêng của biến thể
   */
  @Column({
    name: 'barcode',
    length: 100,
    nullable: true,
    comment: 'Mã vạch riêng của biến thể',
  })
  barcode: string | null;

  /**
   * Các thuộc tính biến thể (VD: màu sắc, kích thước)
   */
  @Column({
    name: 'attributes',
    type: 'jsonb',
    nullable: true,
    comment: 'Các thuộc tính biến thể (VD: màu sắc, kích thước)',
  })
  attributes: any | null;

  /**
   * Giá riêng cho biến thể (nếu có)
   */
  @Column({
    name: 'price',
    type: 'jsonb',
    nullable: true,
    comment: 'Giá riêng cho biến thể (nếu có)',
  })
  price: ProductPrice | null;

  /**
   * Cấu hình vận chuyển riêng cho biến thể
   */
  @Column({
    name: 'shipment_config',
    type: 'jsonb',
    nullable: true,
    comment: 'Cấu hình vận chuyển riêng cho biến thể',
  })
  shipmentConfig: ShipmentConfigType | null;
}
