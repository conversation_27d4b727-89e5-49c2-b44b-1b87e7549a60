import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ProductTypeEnum, PriceTypeEnum, EntityStatusEnum } from '@modules/business/enums';

/**
 * DTO response cho sản phẩm khách hàng
 */
export class CustomerProductResponseDto {
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 123,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'ID người dùng sở hữu sản phẩm',
    example: 456,
  })
  @Expose()
  userId: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam cao cấp',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Mô tả chi tiết sản phẩm',
    example: 'Áo thun nam chất liệu cotton 100%, thiết kế hiện đại',
    required: false,
  })
  @Expose()
  description: string | null;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
  })
  @Expose()
  productType: ProductTypeEnum;

  @ApiProperty({
    description: 'Kiểu giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  @Expose()
  typePrice: PriceTypeEnum;

  @ApiProperty({
    description: 'Giá sản phẩm',
    example: {
      listPrice: 500000,
      salePrice: 450000,
      currency: 'VND'
    },
    required: false,
  })
  @Expose()
  price: any;

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: [String],
    example: ['thời trang', 'nam', 'cotton'],
  })
  @Expose()
  tags: string[];

  @ApiProperty({
    description: 'Trạng thái sản phẩm',
    enum: EntityStatusEnum,
    example: EntityStatusEnum.APPROVED,
  })
  @Expose()
  status: EntityStatusEnum;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1704067200000,
  })
  @Expose()
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối (timestamp)',
    example: 1704067200000,
  })
  @Expose()
  updatedAt: number;

  @ApiProperty({
    description: 'Các trường tuỳ chỉnh',
    example: {
      color: 'Đỏ',
      size: 'L',
      material: 'Cotton'
    },
    required: false,
  })
  @Expose()
  customFields: any;
}

/**
 * DTO response cho danh sách sản phẩm khách hàng với phân trang
 */
export class CustomerProductListResponseDto {
  @ApiProperty({
    description: 'Danh sách sản phẩm',
    type: [CustomerProductResponseDto],
  })
  @Expose()
  @Type(() => CustomerProductResponseDto)
  items: CustomerProductResponseDto[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    example: {
      total: 100,
      page: 1,
      limit: 10,
      totalPages: 10,
    },
  })
  @Expose()
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

/**
 * DTO response cho việc xóa nhiều sản phẩm
 */
export class BulkDeleteCustomerProductResponseDto {
  @ApiProperty({
    description: 'Danh sách kết quả xóa',
    type: [Object],
    example: [
      {
        productId: 123,
        status: 'success',
        message: 'Xóa sản phẩm thành công'
      },
      {
        productId: 124,
        status: 'error',
        message: 'Không tìm thấy sản phẩm'
      }
    ],
  })
  @Expose()
  results: Array<{
    productId: number;
    status: 'success' | 'error';
    message: string;
  }>;

  @ApiProperty({
    description: 'Tổng số sản phẩm được xử lý',
    example: 2,
  })
  @Expose()
  totalProcessed: number;

  @ApiProperty({
    description: 'Số sản phẩm xóa thành công',
    example: 1,
  })
  @Expose()
  successCount: number;

  @ApiProperty({
    description: 'Số sản phẩm xóa thất bại',
    example: 1,
  })
  @Expose()
  failureCount: number;

  @ApiProperty({
    description: 'Thông báo tổng kết',
    example: 'Xóa thành công 1/2 sản phẩm',
  })
  @Expose()
  message: string;
}
