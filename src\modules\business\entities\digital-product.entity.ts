import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng digital_products trong cơ sở dữ liệu
 * Thông tin chi tiết sản phẩm số
 */
@Entity('digital_products')
export class DigitalProduct {
  /**
   * ID sản phẩm số, đồng thời là khóa chính và liên kết với bảng customer_products
   */
  @PrimaryColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Cách giao hàng
   */
  @Column({
    name: 'delivery_method',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '<PERSON><PERSON><PERSON> giao hàng (ví dụ: tải xuống, gửi qua email, cấp quyền truy cập)',
  })
  deliveryMethod: string | null;

  /**
   * Thời điểm giao hàng
   */
  @Column({
    name: 'delivery_time',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Thời điểm giao hàng (ví dụ: ngay lập tức, sau khi thanh toán)',
  })
  deliveryTime: string | null;

  /**
   * Thời gian chờ trước khi giao hàng
   */
  @Column({
    name: 'waiting_time',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Thời gian chờ trước khi giao hàng (ví dụ: xử lý trong 24h)',
  })
  waitingTime: string | null;
}
