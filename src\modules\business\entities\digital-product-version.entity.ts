import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { CustomFieldInterface } from '../interfaces/custom-field.interface';

/**
 * Entity đại diện cho bảng digital_product_versions trong cơ sở dữ liệu
 * Các phiên bản của sản phẩm số
 */
@Entity('digital_product_versions')
export class DigitalProductVersion {
  /**
   * ID phiên bản sản phẩm số
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Khóa ngoại liên kết tới sản phẩm số gốc
   */
  @Column({
    name: 'digital_product_id',
    type: 'bigint',
    nullable: false,
    comment: 'Khóa ngoại liên kết tới sản phẩm số gốc',
  })
  digitalProductId: number;

  /**
   * Tên phiên bản của sản phẩm số
   */
  @Column({
    name: 'version_name',
    length: 255,
    nullable: false,
    comment: 'Tên phiên bản của sản phẩm số (ví dụ: Bản tiêu chuẩn, Bản mở rộng)',
  })
  versionName: string;

  /**
   * Thông tin chi tiết về phiên bản
   */
  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
    comment: 'Thông tin chi tiết về phiên bản',
  })
  description: string | null;

  /**
   * Trường tùy chỉnh lưu dưới dạng JSONB
   */
  @Column({
    name: 'custom_fields',
    type: 'jsonb',
    nullable: true,
    comment: 'Trường tùy chỉnh lưu dưới dạng JSONB (ví dụ: {"ngôn_ngữ": "Tiếng Việt", "định_dạng": "PDF"})',
  })
  customFields: CustomFieldInterface | null;

  /**
   * Liên kết nội dung sản phẩm số
   */
  @Column({
    name: 'content_link',
    length: 1000,
    nullable: true,
    comment: 'Liên kết nội dung sản phẩm số (ví dụ: URL tải về hoặc truy cập nội dung)',
  })
  contentLink: string | null;

  /**
   * Mã SKU cho quản lý kho/phiên bản
   */
  @Column({
    name: 'sku',
    length: 100,
    nullable: true,
    comment: 'Mã SKU cho quản lý kho/phiên bản',
  })
  sku: string | null;

  /**
   * Mã vạch cho phiên bản
   */
  @Column({
    name: 'barcode',
    length: 100,
    nullable: true,
    comment: 'Mã vạch cho phiên bản',
  })
  barcode: string | null;

  /**
   * Số lượng tối thiểu mỗi lần mua
   */
  @Column({
    name: 'min_quantity',
    type: 'integer',
    nullable: true,
    default: 1,
    comment: 'Số lượng tối thiểu mỗi lần mua',
  })
  minQuantity: number | null;

  /**
   * Số lượng tối đa mỗi lần mua
   */
  @Column({
    name: 'max_quantity',
    type: 'integer',
    nullable: true,
    comment: 'Số lượng tối đa mỗi lần mua',
  })
  maxQuantity: number | null;
}
