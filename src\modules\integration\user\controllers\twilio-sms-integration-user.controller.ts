import {
  Controller,
  Post,
  Body,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiBody,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { IntegrationUserService } from '../services';
import { 
  CreateTwilioSmsIntegrationDto,
  IntegrationResponseDto 
} from '../dto/integration';
import { INTEGRATION_ERROR_CODES } from '@modules/integration/exceptions';

/**
 * <PERSON> xử lý các API liên quan đến tích hợp Twilio SMS cho người dùng
 */
@Controller('user/integration/twilio/sms')
@ApiTags(SWAGGER_API_TAGS.USER_INTEGRATION_TWILIO_SMS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
export class TwilioSmsIntegrationUserController {
  constructor(
    private readonly integrationUserService: IntegrationUserService,
  ) {}

  /**
   * Tạo mới tích hợp Twilio SMS
   * @param createDto DTO chứa thông tin tạo mới tích hợp Twilio SMS
   * @param user Thông tin người dùng
   * @returns Thông tin tích hợp Twilio SMS đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới tích hợp twilio sms' })
  @ApiBody({ type: CreateTwilioSmsIntegrationDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo mới tích hợp Twilio SMS thành công',
    schema: ApiResponseDto.getSchema(IntegrationResponseDto),
  })
  @ApiErrorResponse(INTEGRATION_ERROR_CODES.INTEGRATION_CREATE_FAILED)
  async createTwilioSmsIntegration(
    @Body() createDto: CreateTwilioSmsIntegrationDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<IntegrationResponseDto>> {
    const result = await this.integrationUserService.createTwilioSmsIntegration(
      createDto, 
      user.id
    );
    return ApiResponseDto.created(result, 'Tạo mới tích hợp Twilio SMS thành công');
  }
}
