import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServicesModule } from '@shared/services/services.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { IntegrationUserModule } from '@modules/integration/user/integration-user.module';
import {
  UserProduct,
  UserClassification,
  CustomField,
  PhysicalWarehouse,
  UserOrder,
  VirtualWarehouse,
  Warehouse,
  Inventory,
  UserConvert,
  UserConvertCustomer,
  UserAddress,
  ProductAdvancedInfo,
  UserShopInfo,

  CustomerFacebook,
  CustomerWeb,
  File,
  Folder,
} from '../entities';
import { Product } from '@modules/marketplace/entities';
import { Agent } from '@modules/agent/entities/agent.entity';

import {
  UserProductRepository,
  UserOrderRepository,
  WarehouseRepository,
  PhysicalWarehouseRepository,
  VirtualWarehouseRepository,
  InventoryRepository,
  UserClassificationRepository,
  CustomFieldRepository,
  UserConvertRepository,
  UserConvertCustomerRepository,
  UserAddressRepository,
  ProductAdvancedInfoRepository,
  UserShopInfoRepository,

  CustomerFacebookRepository,
  CustomerWebRepository,
  FileRepository,
  FolderRepository,
  BusinessReportRepository,

  // New repositories
  CustomerProductRepository,
  EntityHasMediaRepository,
  DigitalProductRepository,
} from '../repositories';
import { ProductRepository } from '@modules/marketplace/repositories';

import {
  UserProductController,
  UserOrderController,
  UserOrderTrackingController,
  UserShopInfoController,
  UserWarehouseController,
  UserInventoryController,
  UserPhysicalWarehouseController,
  CustomFieldController,
  BusinessIntegrationController,
  ClassificationController,
  UserConvertController,
  UserConvertCustomerController,
  UserFileController,
  UserFolderController,
  UserVirtualWarehouseController,
  BusinessReportController,
  UserAddressController,

  // New controllers
  CustomerProductController,
  EntityHasMediaController,
} from './controllers';

// Import processors and orchestrators
import {
  CreateProductOrchestrator,
  CreateProductProcessor,
  PhysicalProductProcessor,
  DigitalProductProcessor,
  EventProductProcessor,
  ServiceProductProcessor,
  ComboProductProcessor
} from './services/processors/create';
import { DigitalClassificationHandler } from './services/processors/create/digital-classification.handler';
import {
  UpdateProductOrchestrator,
  UpdateProductProcessor,
  PhysicalProductUpdateProcessor,
  DigitalProductUpdateProcessor,
  EventProductUpdateProcessor,
  ServiceProductUpdateProcessor,
  ComboProductUpdateProcessor
} from './services/processors/update';
import { GHTKShipmentController } from '@modules/business/user/controllers';
import { GHNShipmentController } from '@modules/business/user/controllers';
import { ShippingWebhookController } from './controllers/user-order-tracking.controller';

import {
  UserProductService,
  UserOrderService,
  UserWarehouseService,
  UserInventoryService,
  UserPhysicalWarehouseService,
  CustomFieldService,
  BusinessIntegrationService,
  ClassificationService,
  UserConvertService,
  UserConvertCustomerService,
  UserFileService,
  UserFolderService,
  UserVirtualWarehouseService,
  BusinessReportService,
  UserShopInfoService,
  AddressValidationService,
  ShopShippingService,
  UserAddressService,

  // New services
  CustomerProductService,
  EntityHasMediaService,
} from './services';
import { GHTKShipmentService } from '@modules/business/user/services';
import { GHNShipmentService } from './services/ghn-shipment.service';
import { GHTKConfigValidationHelper } from './helpers/ghtk-config-validation.helper';
import { GHNConfigValidationHelper } from './helpers/ghn-config-validation.helper';

import { ValidationHelper, UserProductHelper, BusinessReportHelper, MetadataHelper, ProductValidationHelper } from './helpers';
import { GHTKIntegrationHelper } from './helpers/ghtk-integration.helper';
import { OrderStatusConfigHelper } from './helpers/order-status-config.helper';
import { BusinessIntegrationInterceptor } from './interceptors/business-integration.interceptor';
import { DigitalProduct } from '../entities/digital-product.entity';
import { EntityHasMedia } from '../entities/entity-has-media.entity';
import { CustomerProduct } from '../entities/customer-product.entity';

/**
 * Module quản lý chức năng business cho người dùng
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserProduct,
      UserClassification,
      CustomField,
      PhysicalWarehouse,
      UserOrder,
      VirtualWarehouse,
      Warehouse,
      Inventory,
      UserConvert,
      UserConvertCustomer,
      UserAddress,
      ProductAdvancedInfo,
      UserShopInfo,

      CustomerFacebook,
      CustomerWeb,
      Product,
      Agent,
      File,
      Folder,

      // New entities
      CustomerProduct,
      EntityHasMedia,
      DigitalProduct,
    ]),
    ServicesModule,
    IntegrationUserModule
  ],
  controllers: [
    UserProductController,
    UserOrderController,
    UserOrderTrackingController,
    UserShopInfoController,
    ShippingWebhookController,
    UserWarehouseController,
    UserInventoryController,
    UserPhysicalWarehouseController,
    CustomFieldController,
    BusinessIntegrationController,
    ClassificationController,
    UserConvertController,
    UserConvertCustomerController,
    UserFileController,
    UserFolderController,
    UserVirtualWarehouseController,
    BusinessReportController,
    UserAddressController,
    GHTKShipmentController,
    GHNShipmentController,

    // New controllers
    CustomerProductController,
    EntityHasMediaController
  ],
  providers: [
    // Repositories
    UserProductRepository,
    UserOrderRepository,
    WarehouseRepository,
    PhysicalWarehouseRepository,
    VirtualWarehouseRepository,
    InventoryRepository,
    UserClassificationRepository,
    CustomFieldRepository,
    UserConvertRepository,
    UserConvertCustomerRepository,
    UserAddressRepository,
    ProductAdvancedInfoRepository,
    UserShopInfoRepository,

    CustomerFacebookRepository,
    CustomerWebRepository,
    ProductRepository,
    FileRepository,
    FolderRepository,
    BusinessReportRepository,

    // New repositories
    CustomerProductRepository,
    EntityHasMediaRepository,
    DigitalProductRepository,

    // Helpers
    ValidationHelper,
    UserProductHelper,
    BusinessReportHelper,
    MetadataHelper,
    ProductValidationHelper,
    OrderStatusConfigHelper,

    // Services
    UserProductService,
    UserOrderService,
    UserWarehouseService,
    UserInventoryService,
    UserPhysicalWarehouseService,
    CustomFieldService,
    ClassificationService,
    BusinessIntegrationService,
    UserConvertService,
    UserConvertCustomerService,
    UserFileService,
    UserFolderService,
    UserVirtualWarehouseService,
    BusinessReportService,
    GHTKShipmentService,
    GHNShipmentService,
    UserShopInfoService,
    AddressValidationService,
    ShopShippingService,
    UserAddressService,
    GHTKConfigValidationHelper,
    GHNConfigValidationHelper,
    GHTKIntegrationHelper,

    // New services
    CustomerProductService,
    EntityHasMediaService,

    // Processors and Orchestrators
    CreateProductOrchestrator,
    CreateProductProcessor,
    PhysicalProductProcessor,
    DigitalProductProcessor,
    EventProductProcessor,
    ServiceProductProcessor,
    ComboProductProcessor,
    DigitalClassificationHandler,
    UpdateProductOrchestrator,
    UpdateProductProcessor,
    PhysicalProductUpdateProcessor,
    DigitalProductUpdateProcessor,
    EventProductUpdateProcessor,
    ServiceProductUpdateProcessor,
    ComboProductUpdateProcessor,

    // Interceptors
    {
      provide: APP_INTERCEPTOR,
      useClass: BusinessIntegrationInterceptor
    }
  ],
  exports: [
    TypeOrmModule,
    UserProductService,
    UserOrderService,
    UserWarehouseService,
    UserInventoryService,
    UserPhysicalWarehouseService,
    CustomFieldService,
    ClassificationService,
    BusinessIntegrationService,
    UserConvertService,
    UserConvertCustomerService,
    UserFileService,
    UserFolderService,
    UserVirtualWarehouseService,
    BusinessReportService,
    GHTKShipmentService,
    GHNShipmentService,
    UserProductRepository,

    // New exports
    CustomerProductService,
    CustomerProductRepository,
    EntityHasMediaService,
    EntityHasMediaRepository,
  ],
})
export class BusinessUserModule {}
