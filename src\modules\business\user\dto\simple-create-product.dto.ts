import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { ProductTypeEnum } from '@modules/business/enums';

/**
 * DTO đơn giản cho việc tạo sản phẩm mới
 * Chỉ chứa các trường cơ bản nhất: name, description, productType
 */
export class SimpleCreateProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: '<PERSON>ô tả sản phẩm',
    example: 'Áo thun nam chất liệu cotton cao cấp',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: '<PERSON>ại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
  })
  @IsEnum(ProductTypeEnum)
  @IsNotEmpty()
  productType: ProductTypeEnum;
}
