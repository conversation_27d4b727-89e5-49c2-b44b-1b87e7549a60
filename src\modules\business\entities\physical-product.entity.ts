import { Column, Entity, PrimaryColumn } from 'typeorm';
import { ShipmentConfigType } from '@modules/business/interfaces';

/**
 * Entity đại diện cho bảng physical_products trong cơ sở dữ liệu
 * Thông tin chi tiết sản phẩm vật lý
 */
@Entity('physical_products')
export class PhysicalProduct {
  /**
   * ID sản phẩm vật lý, liên kết với customer_products
   */
  @PrimaryColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Số lượng tồn kho
   */
  @Column({
    name: 'stock_quantity',
    type: 'integer',
    nullable: true,
    comment: 'Số lượng tồn kho',
  })
  stockQuantity: number | null;

  /**
   * Mã SKU
   */
  @Column({
    name: 'sku',
    length: 100,
    nullable: true,
    comment: 'Mã SKU',
  })
  sku: string | null;

  /**
   * Mã vạch sản phẩm
   */
  @Column({
    name: 'barcode',
    length: 100,
    nullable: true,
    comment: 'Mã vạch sản phẩm',
  })
  barcode: string | null;

  /**
   * C<PERSON>u hình vận chuyển
   */
  @Column({
    name: 'shipment_config',
    type: 'jsonb',
    nullable: true,
    comment: 'Cấu hình vận chuyển',
  })
  shipmentConfig: ShipmentConfigType | null;
}
