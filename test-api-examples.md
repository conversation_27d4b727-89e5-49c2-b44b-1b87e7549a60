# API Tạo và X<PERSON>a Sản Phẩm - Hướng dẫn sử dụng

## 1. API Tạo Sản Phẩm Đơn Giản

### Endpoint
```
POST /api/user/products/simple
```

### Headers
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

### Request Body Examples

#### Tạo sản phẩm vật lý
```json
{
  "name": "<PERSON>o thun nam",
  "description": "Áo thun nam chất liệu cotton cao cấp",
  "productType": "PHYSICAL"
}
```

#### Tạo sản phẩm số
```json
{
  "name": "<PERSON>h<PERSON>a học lập trình",
  "description": "<PERSON>h<PERSON>a học lập trình từ cơ bản đến nâng cao",
  "productType": "DIGITAL"
}
```

#### Tạo sự kiện
```json
{
  "name": "<PERSON><PERSON><PERSON> thảo công nghệ",
  "description": "<PERSON>ội thảo về xu hướng công nghệ mới",
  "productType": "EVENT"
}
```

#### Tạo dịch vụ
```json
{
  "name": "Dịch vụ tư vấn",
  "description": "Dịch vụ tư vấn kinh doanh",
  "productType": "SERVICE"
}
```

#### Tạo combo
```json
{
  "name": "Combo học tập",
  "description": "Combo sách và khóa học",
  "productType": "COMBO"
}
```

### Response Success (201)
```json
{
  "success": true,
  "message": "Tạo sản phẩm đơn giản thành công",
  "data": {
    "id": 123,
    "name": "Áo thun nam",
    "description": "Áo thun nam chất liệu cotton cao cấp",
    "productType": "PHYSICAL",
    "typePrice": "HAS_PRICE",
    "price": null,
    "tags": [],
    "images": [],
    "status": "PENDING",
    "createdAt": 1704067200000,
    "updatedAt": 1704067200000,
    "createdBy": 1,
    "metadata": {
      "customFields": []
    }
  }
}
```

### Response Error (400)
```json
{
  "success": false,
  "message": "Dữ liệu đầu vào không hợp lệ",
  "errors": [
    {
      "field": "name",
      "message": "Tên sản phẩm không được để trống"
    }
  ]
}
```

## 2. API Xóa Mềm Sản Phẩm

### Endpoint
```
DELETE /api/user/products/{id}
```

### Headers
```
Authorization: Bearer <JWT_TOKEN>
```

### Path Parameters
- `id` (number): ID của sản phẩm cần xóa

### Example Request
```
DELETE /api/user/products/123
```

### Response Success (200)
```json
{
  "success": true,
  "message": "Sản phẩm đã được xóa thành công",
  "data": null
}
```

### Response Error (404)
```json
{
  "success": false,
  "message": "Không tìm thấy sản phẩm với ID 123"
}
```

## 3. Cách thức hoạt động

### API Tạo Sản Phẩm Đơn Giản
- Chỉ yêu cầu 3 trường cơ bản: `name`, `description` (optional), `productType`
- Tự động thiết lập các giá trị mặc định:
  - `typePrice`: "HAS_PRICE"
  - `price`: null
  - `tags`: []
  - `images`: []
  - `status`: "PENDING" (chờ duyệt)
  - `metadata`: { customFields: [] }

### API Xóa Mềm Sản Phẩm
- Không xóa vật lý khỏi database
- Chỉ cập nhật trạng thái `status` thành "DELETED"
- Cập nhật `updatedAt` thành thời gian hiện tại
- Sản phẩm vẫn tồn tại trong database nhưng không hiển thị trong danh sách

## 4. Validation Rules

### Tạo sản phẩm
- `name`: Bắt buộc, tối đa 255 ký tự
- `description`: Tùy chọn, không giới hạn độ dài
- `productType`: Bắt buộc, phải là một trong: PHYSICAL, DIGITAL, EVENT, SERVICE, COMBO

### Xóa sản phẩm
- Sản phẩm phải tồn tại
- Sản phẩm phải thuộc về người dùng hiện tại
- Không thể xóa sản phẩm đã bị xóa trước đó

## 5. Lưu ý quan trọng

1. **JWT Token**: Cần có JWT token hợp lệ trong header Authorization
2. **User ID**: Được tự động lấy từ JWT token, không cần truyền trong request body
3. **Soft Delete**: Sản phẩm bị xóa vẫn tồn tại trong database với status = "DELETED"
4. **Status**: Sản phẩm mới tạo sẽ có status = "PENDING" (chờ duyệt)
