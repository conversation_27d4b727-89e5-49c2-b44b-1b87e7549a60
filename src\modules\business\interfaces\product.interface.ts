/**
 * Interfaces cho các kiểu dữ liệu JSONB trong module product
 */

/**
 * Interface cho cấu hình giá sản phẩm
 */
export interface ProductPrice {
  /**
   * <PERSON><PERSON><PERSON> gố<PERSON>
   */
  originalPrice?: number;

  /**
   * <PERSON><PERSON><PERSON> b<PERSON>
   */
  salePrice?: number;

  /**
   * <PERSON><PERSON><PERSON> chuỗi (cho STRING_PRICE)
   */
  stringPrice?: string;

  /**
   * Đơn vị tiền tệ
   */
  currency?: string;

  /**
   * <PERSON><PERSON><PERSON>ê<PERSON> yế<PERSON> (để tương thích với code hiện tại)
   */
  listPrice?: number;

  /**
   * <PERSON><PERSON><PERSON> c<PERSON> bản (để tương thích với code hiện tại)
   */
  basePrice?: number;

  /**
   * Cho phép thêm bất kỳ thuộc tính nào khác
   */
  [key: string]: any;
}

/**
 * Interface cho thông tin hình ảnh sản phẩm
 */
export interface ProductImage {
  /**
   * URL của hình ảnh
   */
  url: string;
  
  /**
   * S3 key của hình ảnh
   */
  s3Key: string;
  
  /**
   * Loại MIME của hình ảnh
   */
  mimeType: string;
  
  /**
   * Kích thước file (bytes)
   */
  size?: number;
  
  /**
   * Chiều rộng hình ảnh (pixels)
   */
  width?: number;
  
  /**
   * Chiều cao hình ảnh (pixels)
   */
  height?: number;
  
  /**
   * Có phải là hình ảnh chính không
   */
  isPrimary?: boolean;
}

/**
 * Interface cho danh sách hình ảnh sản phẩm
 * Hỗ trợ cả format array và object để tương thích với code hiện tại
 */
export interface ProductImages {
  /**
   * Danh sách hình ảnh
   */
  items?: ProductImage[];
}

/**
 * Type union cho ProductImages - hỗ trợ cả array và object
 */
export type ProductImagesType = ProductImage[];

/**
 * Interface cho tag sản phẩm
 */
export interface ProductTag {
  /**
   * ID của tag
   */
  id?: number;
  
  /**
   * Tên tag
   */
  name: string;
  
  /**
   * Màu sắc tag
   */
  color?: string;
}

/**
 * Interface cho danh sách tags sản phẩm
 * Hỗ trợ cả format array và object để tương thích với code hiện tại
 */
export interface ProductTags {
  /**
   * Danh sách tags
   */
  items?: ProductTag[];
}

/**
 * Type union cho ProductTags - hỗ trợ cả array và object
 */
export type ProductTagsType = ProductTags | ProductTag[] | string[];

/**
 * Interface cho cấu hình vận chuyển
 */
export interface ShipmentConfig {
  /**
   * Chiều rộng (cm)
   */
  widthCm: number;

  /**
   * Chiều cao (cm)
   */
  heightCm: number;

  /**
   * Chiều dài (cm)
   */
  lengthCm: number;

  /**
   * Trọng lượng (gram)
   */
  weightGram: number;
}

/**
 * Type union cho ShipmentConfig - hỗ trợ cả required và optional fields
 */
export type ShipmentConfigType = ShipmentConfig | any;

/**
 * Interface cho custom field
 */
export interface CustomField {
  /**
   * ID của custom field
   */
  id: number;
  
  /**
   * Tên field
   */
  name: string;
  
  /**
   * Loại dữ liệu
   */
  dataType: string;
  
  /**
   * Giá trị
   */
  value: any;
  
  /**
   * Có bắt buộc không
   */
  required?: boolean;
}

/**
 * Interface cho metadata sản phẩm
 */
export interface ProductMetadata {
  /**
   * Danh sách custom fields
   */
  customFields: CustomField[];
  
  /**
   * Thông tin bổ sung khác
   */
  [key: string]: any;
}

/**
 * Interface cho digital fulfillment flow
 */
export interface DigitalFulfillmentFlow {
  /**
   * Loại fulfillment
   */
  type?: 'AUTOMATIC' | 'MANUAL';

  /**
   * Phương thức giao hàng (để tương thích với DTO hiện tại)
   */
  deliveryMethod?: string;

  /**
   * Thời gian giao hàng (để tương thích với DTO hiện tại)
   */
  deliveryTiming?: string;

  /**
   * Trạng thái truy cập (để tương thích với DTO hiện tại)
   */
  accessStatus?: string;

  /**
   * Cấu hình tự động
   */
  automaticConfig?: {
    /**
     * Gửi email tự động
     */
    sendEmail: boolean;

    /**
     * Template email
     */
    emailTemplate?: string;

    /**
     * Delay time (seconds)
     */
    delaySeconds?: number;
  };

  /**
   * Cấu hình thủ công
   */
  manualConfig?: {
    /**
     * Hướng dẫn xử lý
     */
    instructions: string;

    /**
     * Thời gian xử lý tối đa (hours)
     */
    maxProcessingHours?: number;
  };

  /**
   * Cho phép thêm bất kỳ thuộc tính nào khác
   */
  [key: string]: any;
}

/**
 * Interface cho digital output
 */
export interface DigitalOutput {
  /**
   * Loại output
   */
  type?: 'DOWNLOAD_LINK' | 'ACCESS_CODE' | 'ACCOUNT_INFO' | 'CONTENT';

  /**
   * Loại output (để tương thích với DTO hiện tại)
   */
  outputType?: string;

  /**
   * Thông tin download link
   */
  downloadInfo?: {
    /**
     * URL download
     */
    url: string;

    /**
     * Thời gian hết hạn (timestamp)
     */
    expiresAt?: number;

    /**
     * Số lần download tối đa
     */
    maxDownloads?: number;
  };

  /**
   * Thông tin access code
   */
  accessCodeInfo?: {
    /**
     * Mã truy cập
     */
    code: string;

    /**
     * Hướng dẫn sử dụng
     */
    instructions: string;
  };

  /**
   * Thông tin tài khoản
   */
  accountInfo?: {
    /**
     * Username
     */
    username: string;

    /**
     * Password
     */
    password: string;

    /**
     * URL đăng nhập
     */
    loginUrl?: string;
  };

  /**
   * Nội dung trực tiếp
   */
  content?: {
    /**
     * Tiêu đề
     */
    title: string;

    /**
     * Nội dung
     */
    body: string;

    /**
     * Định dạng
     */
    format: 'TEXT' | 'HTML' | 'MARKDOWN';
  };

  /**
   * Cho phép thêm bất kỳ thuộc tính nào khác
   */
  [key: string]: any;
}

/**
 * Interface cho ticket type
 */
export interface TicketType {
  /**
   * ID của ticket type
   */
  id?: number;
  
  /**
   * Tên loại vé
   */
  name: string;
  
  /**
   * Mô tả
   */
  description?: string;
  
  /**
   * Giá vé
   */
  price: number;
  
  /**
   * Số lượng vé có sẵn
   */
  quantity: number;
  
  /**
   * Số lượng vé đã bán
   */
  soldQuantity?: number;
  
  /**
   * Thời gian bắt đầu bán (timestamp)
   */
  saleStartTime?: number;
  
  /**
   * Thời gian kết thúc bán (timestamp)
   */
  saleEndTime?: number;

  /**
   * Hình ảnh ticket type
   */
  images?: ProductImage[];
}

/**
 * Interface cho service package
 */
export interface ServicePackage {
  /**
   * ID của service package
   */
  id?: number;
  
  /**
   * Tên gói dịch vụ
   */
  name: string;
  
  /**
   * Mô tả
   */
  description?: string;
  
  /**
   * Giá gói
   */
  price: number;
  
  /**
   * Thời gian thực hiện (hours)
   */
  durationHours?: number;
  
  /**
   * Danh sách tính năng
   */
  features?: string[];
  
  /**
   * Hình ảnh gói dịch vụ
   */
  images?: ProductImage[];
  
  /**
   * Có phải gói phổ biến không
   */
  isPopular?: boolean;
}

/**
 * Interface cho combo info
 */
export interface ComboInfo {
  /**
   * Danh sách sản phẩm trong combo
   */
  info: ComboItem[];
}

/**
 * Interface cho item trong combo
 */
export interface ComboItem {
  /**
   * ID sản phẩm
   */
  productId: number;

  /**
   * Tên sản phẩm
   */
  productName?: string;

  /**
   * Số lượng (để tương thích với code hiện tại)
   */
  quantity?: number;

  /**
   * Tổng số lượng (để tương thích với DTO)
   */
  total: number;

  /**
   * Giá gốc của sản phẩm
   */
  originalPrice?: number;

  /**
   * Giá trong combo
   */
  comboPrice?: number;

  /**
   * Loại sản phẩm
   */
  productType?: string;

  /**
   * Hình ảnh đại diện
   */
  image?: ProductImage;
}
