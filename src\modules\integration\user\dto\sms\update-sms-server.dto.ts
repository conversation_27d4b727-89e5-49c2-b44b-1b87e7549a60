import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsObject, IsString } from 'class-validator';

/**
 * DTO cho việc cập nhật cấu hình máy chủ SMS
 */
export class UpdateSmsServerDto {
  @ApiProperty({
    description: 'Tên tích hợp do người dùng đặt (sẽ được lưu vào integration_name)',
    example: 'config FPT SMS updated',
  })
  @IsOptional()
  @IsString({ message: 'Tên tích hợp phải là chuỗi' })
  integration_name?: string;

  @ApiProperty({
    description: 'Thông tin cấu hình SMS chi tiết (type sẽ tự động được set là "SMS")',
    type: 'object',
    additionalProperties: true,
    example: {
      integrationName: 'FPT_SMS',
      apiKey: 'your-api-key-here',
      endpoint: 'http://api.fpt.net/api',
      clientId: 'your-client-id'
    },
  })
  @IsOptional()
  @IsObject({ message: 'Thông tin cấu hình phải là đối tượng' })
  info?: Record<string, any>;
}


