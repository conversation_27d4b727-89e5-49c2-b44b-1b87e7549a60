import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import {
  EntityStatusEnum,
  PriceTypeEnum,
  ProductTypeEnum,
} from '@modules/business/enums';
import {
  ProductTagsType,
} from '@modules/business/interfaces';
import { HasPriceDto, StringPriceDto } from '../user/dto';
import { CustomFieldInterface } from '../interfaces/custom-field.interface';

// Union type cho price
export type ProductPriceType = HasPriceDto | StringPriceDto;

/**
 * Entity đại diện cho bảng customer_products trong cơ sở dữ liệu
 * Bảng lưu thông tin sản phẩm do người dùng tạo (customer product)
 */
@Entity('customer_products')
export class CustomerProduct {
  /**
   * ID tự tăng của sản phẩm
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID người dùng sở hữu sản phẩm
   */
  @Column({
    name: 'user_id',
    type: 'bigint',
    nullable: true,
    comment: 'ID người dùng sở hữu sản phẩm',
  })
  userId: number | null;

  /**
   * Tên sản phẩm
   */
  @Column({
    name: 'name',
    length: 500,
    nullable: false,
    comment: 'Tên sản phẩm',
  })
  name: string;

  /**
   * Mô tả chi tiết sản phẩm
   */
  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
    comment: 'Mô tả chi tiết sản phẩm',
  })
  description: string | null;

  /**
   * Giá sản phẩm, lưu dưới dạng JSONB để linh hoạt
   */
  @Column({
    name: 'price',
    type: 'jsonb',
    nullable: true,
    comment: 'Giá sản phẩm, lưu dưới dạng JSONB để linh hoạt (VD: giá theo vùng, loại...)',
  })
  price: ProductPriceType | null;

  /**
   * Kiểu giá sản phẩm
   */
  @Column({
    name: 'type_price',
    length: 20,
    nullable: true,
    comment: 'Kiểu giá sản phẩm (ví dụ: cố định, theo giờ...)',
  })
  typePrice: PriceTypeEnum;

  /**
   * Danh sách tag liên quan đến sản phẩm
   */
  @Column({
    name: 'tags',
    type: 'jsonb',
    nullable: true,
    comment: 'Danh sách tag liên quan đến sản phẩm, lưu dưới dạng JSONB',
  })
  tags: string[];

  /**
   * Thời điểm tạo (timestamp dạng bigint)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời điểm tạo (timestamp dạng bigint)',
  })
  createdAt: number | null;

  /**
   * Thời điểm cập nhật (timestamp dạng bigint)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời điểm cập nhật (timestamp dạng bigint)',
  })
  updatedAt: number | null;

  /**
   * Trạng thái sản phẩm
   */
  @Column({
    name: 'status',
    length: 20,
    nullable: true,
    comment: 'Trạng thái sản phẩm (VD: active, inactive, draft)',
  })
  status: EntityStatusEnum;

  /**
   * Loại sản phẩm
   */
  @Column({
    name: 'product_type',
    length: 20,
    nullable: true,
    comment: 'Loại sản phẩm (VD: physical, digital, service)',
  })
  productType: ProductTypeEnum;

  /**
   * Các trường tuỳ chỉnh theo từng sản phẩm
   */
  @Column({
    name: 'custom_fields',
    type: 'jsonb',
    nullable: true,
    comment: 'Các trường tuỳ chỉnh theo từng sản phẩm (lưu JSONB)',
  })
  customFields: CustomFieldInterface | null;
}
