import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  AgentUserRepository
} from '@modules/agent/repositories';
import { ConvertConfig } from '@modules/agent/interfaces/convert-config.interface';
import { Transactional } from 'typeorm-transactional';
import {
  UpdateConversionDto,
  ConversionResponseDto
} from '../dto/conversion';
import { ConversionMapper } from '../mappers';

/**
 * Service xử lý các thao tác liên quan đến conversion config của agent cho người dùng
 */
@Injectable()
export class ConversionUserService {
  private readonly logger = new Logger(ConversionUserService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
  ) { }

  /**
   * Lấy thông tin conversion config của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin conversion config
   */
  @Transactional()
  async getConversion(
    agentId: string,
    userId: number,
  ): Promise<ConversionResponseDto> {
    try {
      // Chỉ lấy convert_config thay vì toàn bộ thông tin agent
      const convertConfig = await this.agentUserRepository.findConvertConfigByIdAndUserId(agentId, userId);

      if (convertConfig === null) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Đảm bảo convertConfig luôn là array, xử lý trường hợp legacy data
      let finalConvertConfig: ConvertConfig[] = [];

      if (Array.isArray(convertConfig)) {
        finalConvertConfig = convertConfig;
      } else if (convertConfig && typeof convertConfig === 'object') {
        // Trường hợp legacy data là object, tự động fix và sử dụng array rỗng
        this.logger.warn(`Agent ${agentId} có convertConfig là object thay vì array, tự động fix thành array`);

        // Tự động fix legacy data trong background
        try {
          await this.fixLegacyConvertConfig(agentId, userId);
        } catch (fixError) {
          this.logger.error(`Không thể fix legacy data cho agent ${agentId}: ${fixError.message}`);
        }

        finalConvertConfig = [];
      } else {
        // Trường hợp null hoặc undefined
        finalConvertConfig = [];
      }

      // Chuyển đổi sang response DTO
      const response = ConversionMapper.toResponseDto(finalConvertConfig);

      this.logger.log(`Lấy conversion config thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy conversion config agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật conversion config của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin conversion config cần cập nhật
   * @returns Thông tin conversion config đã cập nhật
   */
  @Transactional()
  async updateConversion(
    agentId: string,
    userId: number,
    updateDto: UpdateConversionDto,
  ): Promise<ConversionResponseDto> {
    try {
      // Kiểm tra agent có thuộc về user không - chỉ lấy convert_config
      const existingConvertConfig = await this.agentUserRepository.findConvertConfigByIdAndUserId(agentId, userId);

      if (existingConvertConfig === null) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Chuyển đổi DTO sang entity format
      const updatedConvertConfig = ConversionMapper.fromDtoToEntity(updateDto.convertConfig);

      // Validate conversion config
      if (!ConversionMapper.validateConvertConfig(updatedConvertConfig)) {
        throw new AppException(
          AGENT_ERROR_CODES.INVALID_MULTI_AGENT_CONFIG, // Sử dụng error code có sẵn
          'Cấu hình conversion không hợp lệ'
        );
      }

      // Cập nhật conversion config trong database
      await this.agentUserRepository.createQueryBuilder()
        .update()
        .set({
          convertConfig: updatedConvertConfig
        })
        .where('id = :id', { id: agentId })
        .andWhere('userId = :userId', { userId })
        .execute();

      // Chuyển đổi sang response DTO với config đã cập nhật
      const response = ConversionMapper.toResponseDto(updatedConvertConfig);

      this.logger.log(`Cập nhật conversion config thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật conversion config agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Reset conversion config về mặc định
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin conversion config mặc định
   */
  @Transactional()
  async resetConversion(
    agentId: string,
    userId: number,
  ): Promise<ConversionResponseDto> {
    try {
      // Kiểm tra agent có thuộc về user không - chỉ lấy convert_config
      const existingConvertConfig = await this.agentUserRepository.findConvertConfigByIdAndUserId(agentId, userId);

      if (existingConvertConfig === null) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Tạo conversion config mặc định
      const defaultConvertConfig = ConversionMapper.createDefaultConvertConfig();

      // Cập nhật conversion config trong database
      await this.agentUserRepository.createQueryBuilder()
        .update()
        .set({
          convertConfig: defaultConvertConfig
        })
        .where('id = :id', { id: agentId })
        .andWhere('userId = :userId', { userId })
        .execute();

      // Chuyển đổi sang response DTO
      const response = ConversionMapper.toResponseDto(defaultConvertConfig);

      this.logger.log(`Reset conversion config thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi reset conversion config agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Fix dữ liệu legacy - chuyển convertConfig từ object thành array
   * @param agentId ID của agent cần fix
   * @param userId ID của người dùng
   */
  @Transactional()
  async fixLegacyConvertConfig(agentId: string, userId: number): Promise<void> {
    try {
      // Lấy convertConfig hiện tại
      const convertConfig = await this.agentUserRepository.findConvertConfigByIdAndUserId(agentId, userId);

      if (convertConfig === null) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Nếu convertConfig không phải array, fix nó
      if (!Array.isArray(convertConfig)) {
        this.logger.warn(`Fixing legacy convertConfig for agent ${agentId}: converting object to array`);

        // Cập nhật thành array rỗng
        await this.agentUserRepository.createQueryBuilder()
          .update()
          .set({
            convertConfig: []
          })
          .where('id = :id', { id: agentId })
          .andWhere('userId = :userId', { userId })
          .execute();

        this.logger.log(`Fixed legacy convertConfig for agent ${agentId}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi fix legacy convertConfig cho agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }


}
