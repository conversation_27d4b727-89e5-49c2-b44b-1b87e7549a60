import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { DigitalProduct } from '../entities/digital-product.entity';
import { PaginatedResult } from '@common/response';

/**
 * Repository cho DigitalProduct entity
 * Xử lý các thao tác database cho sản phẩm số
 */
@Injectable()
export class DigitalProductRepository {
  private readonly logger = new Logger(DigitalProductRepository.name);

  constructor(
    @InjectRepository(DigitalProduct)
    private readonly repository: Repository<DigitalProduct>,
  ) {}

  /**
   * Tạo sản phẩm số mới
   * @param data Dữ liệu sản phẩm số
   * @returns Sản phẩm số đã tạo
   */
  async create(data: Partial<DigitalProduct>): Promise<DigitalProduct> {
    const digitalProduct = this.repository.create(data);
    return this.repository.save(digitalProduct);
  }

  /**
   * <PERSON><PERSON><PERSON> sản phẩm số theo ID
   * @param id ID sản phẩm số
   * @returns Sản phẩm số hoặc null
   */
  async findById(id: number): Promise<DigitalProduct | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm sản phẩm số theo ID (alias cho findById)
   * @param digitalProductId ID sản phẩm số
   * @returns Sản phẩm số hoặc null
   */
  async findByProductId(digitalProductId: number): Promise<DigitalProduct | null> {
    return this.repository.findOne({
      where: { id: digitalProductId },
    });
  }

  /**
   * Lấy danh sách sản phẩm số với phân trang và filter
   * @param query Tham số truy vấn
   * @returns Danh sách sản phẩm số với phân trang
   */
  async findAll(query: {
    page?: number;
    limit?: number;
    search?: string;
    deliveryMethod?: string;
  }): Promise<PaginatedResult<DigitalProduct>> {
    const {
      page = 1,
      limit = 10,
      search,
      deliveryMethod,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('digitalProduct');

    // Filter theo deliveryMethod nếu có
    if (deliveryMethod) {
      queryBuilder.andWhere('digitalProduct.deliveryMethod = :deliveryMethod', { deliveryMethod });
    }

    // Tìm kiếm theo ID nếu có
    if (search && !isNaN(Number(search))) {
      queryBuilder.andWhere('digitalProduct.id = :id', { id: Number(search) });
    }

    // Sắp xếp theo ID mới nhất
    queryBuilder.orderBy('digitalProduct.id', 'DESC');

    // Phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Thực hiện truy vấn
    const [items, totalItems] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Cập nhật sản phẩm số
   * @param id ID sản phẩm số
   * @param data Dữ liệu cập nhật
   * @returns Sản phẩm số đã cập nhật
   */
  async update(id: number, data: Partial<DigitalProduct>): Promise<DigitalProduct> {
    await this.repository.update(id, data);

    const updatedProduct = await this.findById(id);
    if (!updatedProduct) {
      throw new Error(`DigitalProduct with ID ${id} not found after update`);
    }

    return updatedProduct;
  }

  /**
   * Xóa sản phẩm số
   * @param id ID sản phẩm số
   */
  async delete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Xóa sản phẩm số theo ID (alias cho delete)
   * @param digitalProductId ID sản phẩm số
   */
  async deleteByProductId(digitalProductId: number): Promise<void> {
    await this.repository.delete(digitalProductId);
  }

  /**
   * Lưu sản phẩm số
   * @param digitalProduct Sản phẩm số cần lưu
   * @returns Sản phẩm số đã lưu
   */
  async save(digitalProduct: DigitalProduct): Promise<DigitalProduct> {
    return this.repository.save(digitalProduct);
  }

  /**
   * Tìm sản phẩm số theo danh sách IDs
   * @param ids Danh sách ID
   * @returns Danh sách sản phẩm số
   */
  async findByIds(ids: number[]): Promise<DigitalProduct[]> {
    if (!ids.length) return [];

    return this.repository.find({
      where: { id: In(ids) },
    });
  }

  /**
   * Đếm tổng số sản phẩm số
   * @returns Số lượng sản phẩm số
   */
  async countByProductId(): Promise<number> {
    return this.repository.count();
  }

  /**
   * Tìm sản phẩm số theo deliveryMethod
   * @param deliveryMethod Phương thức giao hàng
   * @returns Danh sách sản phẩm số
   */
  async findByDeliveryMethod(deliveryMethod: string): Promise<DigitalProduct[]> {
    return this.repository.find({
      where: { deliveryMethod },
    });
  }

  /**
   * Tìm sản phẩm số theo deliveryTime (thay cho timingType)
   * @param deliveryTime Thời điểm giao hàng
   * @returns Danh sách sản phẩm số
   */
  async findByTimingType(deliveryTime: string): Promise<DigitalProduct[]> {
    return this.repository.find({
      where: { deliveryTime },
    });
  }

  /**
   * Kiểm tra sản phẩm số có tồn tại theo ID
   * @param id ID sản phẩm số
   * @returns true nếu tồn tại, false nếu không
   */
  async existsByProductId(id: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { id },
    });
    return count > 0;
  }

  /**
   * Lấy sản phẩm số với thông tin sản phẩm chính
   * @param id ID sản phẩm số
   * @returns Sản phẩm số với thông tin sản phẩm chính
   */
  async findByIdWithProduct(id: number): Promise<DigitalProduct | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['product'],
    });
  }

  /**
   * Lấy danh sách sản phẩm số với thông tin sản phẩm chính
   * @param query Tham số truy vấn
   * @returns Danh sách sản phẩm số với thông tin sản phẩm chính
   */
  async findAllWithProduct(query: {
    page?: number;
    limit?: number;
    search?: string;
    deliveryMethod?: string;
  }): Promise<PaginatedResult<DigitalProduct>> {
    const {
      page = 1,
      limit = 10,
      search,
      deliveryMethod,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('digitalProduct');

    // Filter theo deliveryMethod nếu có
    if (deliveryMethod) {
      queryBuilder.andWhere('digitalProduct.deliveryMethod = :deliveryMethod', { deliveryMethod });
    }

    // Tìm kiếm theo ID nếu có
    if (search && !isNaN(Number(search))) {
      queryBuilder.andWhere('digitalProduct.id = :id', { id: Number(search) });
    }

    // Sắp xếp theo ID mới nhất
    queryBuilder.orderBy('digitalProduct.id', 'DESC');

    // Phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Thực hiện truy vấn
    const [items, totalItems] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }
}
