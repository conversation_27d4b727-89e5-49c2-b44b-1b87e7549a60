import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng entity_has_media trong cơ sở dữ liệu
 * Bảng chứa các thực thể với media
 */
@Entity('entity_has_media')
export class EntityHasMedia {
  /**
   * ID tự tăng
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID sản phẩm
   */
  @Column({
    name: 'product_id',
    type: 'bigint',
    nullable: true,
    comment: 'ID sản phẩm',
  })
  productId: number | null;

  /**
   * Biến thể sản phẩm vật lý
   */
  @Column({
    name: 'physical_varial',
    type: 'integer',
    nullable: true,
    comment: 'Biến thể sản phẩm vật lý',
  })
  physicalVarial: number | null;

  /**
   * <PERSON>iến thể của vé
   */
  @Column({
    name: 'ticket_varial',
    type: 'bigint',
    nullable: true,
    comment: 'Biến thể của vé',
  })
  ticketVarial: number | null;

  /**
   * Phiên bản của sản phẩm số
   */
  @Column({
    name: 'version_id',
    type: 'bigint',
    nullable: true,
    comment: 'Phiên bản của sản phẩm số',
  })
  versionId: number | null;

  /**
   * ID sản phẩm combo
   */
  @Column({
    name: 'product_combo_id',
    type: 'bigint',
    nullable: true,
    comment: 'sản phẩm combo',
  })
  productComboId: number | null;

  /**
   * ID của gói dịch vụ trong sản phẩm dịch vụ
   */
  @Column({
    name: 'product_plan_varial_id',
    type: 'bigint',
    nullable: true,
    comment: 'id của gói dịch vụ trong sản phẩm dịch vụ',
  })
  productPlanVarialId: number | null;

  /**
   * ID media
   */
  @Column({
    name: 'media_id',
    type: 'bigint',
    nullable: true,
    comment: 'ID media',
  })
  mediaId: number | null;
}
