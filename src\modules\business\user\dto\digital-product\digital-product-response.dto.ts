import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { DeliveryMethodType } from './create-digital-product.dto';

/**
 * DTO response cho sản phẩm số
 */
export class DigitalProductResponseDto {
  @ApiProperty({
    description: 'ID sản phẩm số',
    example: 123,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'ID sản phẩm chính',
    example: 456,
  })
  @Expose()
  productId: number;

  @ApiProperty({
    description: 'Phương thức giao hàng',
    example: 'DASHBOARD',
    enum: DeliveryMethodType,
    required: false,
  })
  @Expose()
  deliveryMethod: DeliveryMethodType | null;

  @ApiProperty({
    description: 'Thời điểm giao hàng',
    example: 'IMMEDIATE',
    required: false,
  })
  @Expose()
  deliveryTime: string | null;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> thời gian',
    example: 'IMMEDIATE',
    required: false,
  })
  @Expose()
  timingType: string | null;

  @ApiProperty({
    description: 'Thông tin giao hàng số',
    example: {
      deliveryMethod: 'DASHBOARD',
      deliveryTiming: 'IMMEDIATE'
    },
    required: false,
  })
  @Expose()
  deliveryInfo: any;

  @ApiProperty({
    description: 'Thông tin đầu ra số',
    example: {
      outputType: 'ACCESS_CODE',
      accessLink: 'https://course.example.com/activate',
      usageInstructions: 'Sử dụng mã code để kích hoạt khóa học'
    },
    required: false,
  })
  @Expose()
  outputInfo: any;

  @ApiProperty({
    description: 'Số lượt mua',
    example: 25,
    required: false,
  })
  @Expose()
  purchaseCount: number | null;

  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: {
      features: ['Truy cập trọn đời', 'Hỗ trợ 24/7'],
      requirements: ['Kết nối internet', 'Trình duyệt web']
    },
    required: false,
  })
  @Expose()
  additionalInfo: any;
}

/**
 * DTO response cho danh sách sản phẩm số với phân trang
 */
export class DigitalProductListResponseDto {
  @ApiProperty({
    description: 'Danh sách sản phẩm số',
    type: [DigitalProductResponseDto],
  })
  @Expose()
  items: DigitalProductResponseDto[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    example: {
      total: 100,
      page: 1,
      limit: 10,
      totalPages: 10,
    },
  })
  @Expose()
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

/**
 * DTO response cho việc xóa nhiều sản phẩm số
 */
export class BulkDeleteDigitalProductResponseDto {
  @ApiProperty({
    description: 'Danh sách kết quả xóa',
    type: [Object],
    example: [
      {
        digitalProductId: 123,
        status: 'success',
        message: 'Xóa sản phẩm số thành công'
      },
      {
        digitalProductId: 124,
        status: 'error',
        message: 'Không tìm thấy sản phẩm số'
      }
    ],
  })
  @Expose()
  results: Array<{
    digitalProductId: number;
    status: 'success' | 'error';
    message: string;
  }>;

  @ApiProperty({
    description: 'Tổng số sản phẩm số được xử lý',
    example: 2,
  })
  @Expose()
  totalProcessed: number;

  @ApiProperty({
    description: 'Số sản phẩm số xóa thành công',
    example: 1,
  })
  @Expose()
  successCount: number;

  @ApiProperty({
    description: 'Số sản phẩm số xóa thất bại',
    example: 1,
  })
  @Expose()
  failureCount: number;

  @ApiProperty({
    description: 'Thông báo tổng kết',
    example: 'Xóa thành công 1/2 sản phẩm số',
  })
  @Expose()
  message: string;
}
