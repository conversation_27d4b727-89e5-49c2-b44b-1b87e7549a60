import { CustomFieldTypeEnum } from "../enums";

export interface CustomFieldInterface {
  /**
   * ID của custom field
   */
  id: number;
  
  /**
   * Tên field
   */
  configId: string;
  
  /**
   * Loại dữ liệu
   */
  type: CustomFieldTypeEnum;
  
  /**
   * Giá trị
   */
  value: any;
  
  /**
   * <PERSON><PERSON> bắt buộc không
   */
  required?: boolean;

  label: string;

  configJson: any;
}