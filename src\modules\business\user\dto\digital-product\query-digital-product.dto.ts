import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, Min, Max, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { DeliveryMethodType } from './create-digital-product.dto';

/**
 * DTO cho việc truy vấn danh sách sản phẩm số
 */
export class QueryDigitalProductDto {
  @ApiProperty({
    description: 'Số trang',
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng item trên mỗi trang',
    example: 10,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm (tìm trong tên sản phẩm)',
    example: 'khóa học',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'ID sản phẩm chính (filter theo sản phẩm)',
    example: 123,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  productId?: number;

  @ApiProperty({
    description: 'Phương thức giao hàng (filter)',
    example: 'DASHBOARD',
    enum: DeliveryMethodType,
    required: false,
  })
  @IsOptional()
  @IsEnum(DeliveryMethodType)
  deliveryMethod?: DeliveryMethodType;

  @ApiProperty({
    description: 'Loại thời gian (filter)',
    example: 'IMMEDIATE',
    required: false,
  })
  @IsOptional()
  @IsString()
  timingType?: string;

  @ApiProperty({
    description: 'Sắp xếp theo trường',
    example: 'id',
    enum: ['id', 'purchaseCount'],
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: 'id' | 'purchaseCount' = 'id';

  @ApiProperty({
    description: 'Thứ tự sắp xếp',
    example: 'DESC',
    enum: ['ASC', 'DESC'],
    required: false,
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}
