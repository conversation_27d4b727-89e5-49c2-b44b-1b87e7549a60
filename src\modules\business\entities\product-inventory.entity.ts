import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng product_inventory trong cơ sở dữ liệu
 * Bảng quản lý tồn kho sản phẩm tại các kho khác nhau
 */
@Entity('product_inventory')
export class ProductInventory {
  /**
   * Khóa chính tự tăng
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID sản phẩm, liên kết với bảng sản phẩm
   */
  @Column({
    name: 'product_id',
    type: 'bigint',
    nullable: false,
    comment: 'ID sản phẩm, liên kết với bảng sản phẩm',
  })
  productId: number;

  /**
   * ID kho chứa sản phẩm
   */
  @Column({
    name: 'warehouse_id',
    type: 'bigint',
    nullable: true,
    comment: 'ID kho chứa sản phẩm, c<PERSON> thể null nếu chưa xác định kho cụ thể',
  })
  warehouseId: number | null;

  /**
   * <PERSON><PERSON> lượng tồn kho của sản phẩm tại kho tương ứng
   */
  @Column({
    name: 'quantity',
    type: 'integer',
    nullable: false,
    default: 0,
    comment: 'Số lượng tồn kho của sản phẩm tại kho tương ứng',
  })
  quantity: number;

  /**
   * Số lượng sản phẩm đang được xử lý
   */
  @Column({
    name: 'processing_quantity',
    type: 'integer',
    nullable: false,
    default: 0,
    comment: 'Số lượng sản phẩm đang được xử lý (ví dụ: đang đóng gói, vận chuyển...)',
  })
  processingQuantity: number;

  /**
   * Số lượng sản phẩm bị lỗi, hỏng hoặc không thể sử dụng
   */
  @Column({
    name: 'error_quantity',
    type: 'integer',
    nullable: false,
    default: 0,
    comment: 'Số lượng sản phẩm bị lỗi, hỏng hoặc không thể sử dụng',
  })
  errorQuantity: number;

  /**
   * Thời điểm cập nhật tồn kho gần nhất
   */
  @Column({
    name: 'updated_at',
    type: 'timestamp',
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    comment: 'Thời điểm cập nhật tồn kho gần nhất',
  })
  updatedAt: Date | null;
}
