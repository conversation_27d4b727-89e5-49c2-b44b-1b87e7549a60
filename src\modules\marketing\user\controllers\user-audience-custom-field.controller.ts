import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { UserAudienceCustomFieldService } from '@modules/marketing/user/services';
import { 
  CreateCustomFieldDto, 
  UpdateCustomFieldDto, 
  CustomFieldResponseDto 
} from '../dto/audience';
import { ApiResponseDto } from '@common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers/response.helper';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { BulkUpdateCustomFieldsDto } from '@modules/marketing/user/dto';

/**
 * Controller xử lý các API liên quan đến giá trị trường tùy chỉnh của audience
 */
@ApiTags(SWAGGER_API_TAGS.USER_MARKETING_CUSTOM_FIELD)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/marketing/audiences/:audienceId/custom-fields')
export class UserAudienceCustomFieldController {
  constructor(private readonly customFieldService: UserAudienceCustomFieldService) {}

  /**
   * Tạo mới giá trị trường tùy chỉnh cho audience
   * @param user Thông tin người dùng
   * @param audienceId ID của audience
   * @param createDto Dữ liệu tạo mới
   * @returns Thông tin giá trị trường tùy chỉnh đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới giá trị trường tùy chỉnh cho audience' })
  @ApiResponse({
    status: 201,
    description: 'Giá trị trường tùy chỉnh đã được tạo thành công',
    schema: ApiResponseDto.getSchema(CustomFieldResponseDto),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_ALREADY_EXISTS,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
  )
  async create(
    @CurrentUser() user: JwtPayload,
    @Param('audienceId', ParseIntPipe) audienceId: number,
    @Body() createDto: CreateCustomFieldDto,
  ): Promise<ApiResponseDto<CustomFieldResponseDto>> {
    const result = await this.customFieldService.create(user.id, audienceId, createDto);
    return wrapResponse(result, 'Tạo giá trị trường tùy chỉnh thành công');
  }

  /**
   * Cập nhật hàng loạt giá trị trường tùy chỉnh cho audience
   * Sẽ xóa tất cả các giá trị hiện có và thay thế bằng các giá trị mới
   * @param user Thông tin người dùng
   * @param audienceId ID của audience
   * @param updateDto Dữ liệu cập nhật
   * @returns Danh sách các trường tùy chỉnh đã cập nhật
   */
  @Put()
  @ApiOperation({ summary: 'Cập nhật hàng loạt giá trị trường tùy chỉnh cho audience' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách giá trị trường tùy chỉnh đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema([CustomFieldResponseDto]),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
  )
  async bulkUpdate(
    @CurrentUser() user: JwtPayload,
    @Param('audienceId', ParseIntPipe) audienceId: number,
    @Body() updateDto: BulkUpdateCustomFieldsDto,
  ): Promise<ApiResponseDto<CustomFieldResponseDto[]>> {
    const result = await this.customFieldService.bulkUpdate(user.id, audienceId, updateDto);
    return wrapResponse(result, 'Cập nhật hàng loạt giá trị trường tùy chỉnh thành công');
  }

  /**
   * Cập nhật giá trị trường tùy chỉnh
   * @param user Thông tin người dùng
   * @param audienceId ID của audience
   * @param id ID của giá trị trường tùy chỉnh
   * @param updateDto Dữ liệu cập nhật
   * @returns Thông tin giá trị trường tùy chỉnh đã cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật giá trị trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Giá trị trường tùy chỉnh đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(CustomFieldResponseDto),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
  )
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('audienceId', ParseIntPipe) audienceId: number,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCustomFieldDto,
  ): Promise<ApiResponseDto<CustomFieldResponseDto>> {
    const result = await this.customFieldService.update(user.id, id, updateDto.fieldValue);
    return wrapResponse(result, 'Cập nhật giá trị trường tùy chỉnh thành công');
  }

  /**
   * Xóa giá trị trường tùy chỉnh
   * @param user Thông tin người dùng
   * @param audienceId ID của audience
   * @param id ID của giá trị trường tùy chỉnh
   * @returns Thông tin giá trị trường tùy chỉnh đã xóa
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa giá trị trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Giá trị trường tùy chỉnh đã được xóa thành công',
    schema: ApiResponseDto.getSchema(CustomFieldResponseDto),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_DELETE_FAILED,
  )
  async delete(
    @CurrentUser() user: JwtPayload,
    @Param('audienceId', ParseIntPipe) audienceId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<CustomFieldResponseDto>> {
    const result = await this.customFieldService.delete(user.id, id);
    return wrapResponse(result, 'Xóa giá trị trường tùy chỉnh thành công');
  }

  /**
   * Lấy danh sách giá trị trường tùy chỉnh của audience
   * @param user Thông tin người dùng
   * @param audienceId ID của audience
   * @returns Danh sách giá trị trường tùy chỉnh
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách giá trị trường tùy chỉnh của audience' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách giá trị trường tùy chỉnh thành công',
    schema: ApiResponseDto.getSchema([CustomFieldResponseDto]),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_QUERY_FAILED,
  )
  async findByAudienceId(
    @CurrentUser() user: JwtPayload,
    @Param('audienceId', ParseIntPipe) audienceId: number,
  ): Promise<ApiResponseDto<CustomFieldResponseDto[]>> {
    const result = await this.customFieldService.findByAudienceId(audienceId);
    return wrapResponse(result, 'Lấy danh sách giá trị trường tùy chỉnh thành công');
  }
}
