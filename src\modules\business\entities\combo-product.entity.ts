import { Column, Entity, PrimaryColumn } from 'typeorm';
import { ProductPrice } from '@modules/business/interfaces';

/**
 * Entity đại diện cho bảng combo_products trong cơ sở dữ liệu
 * Bảng chứa thông tin về sản phẩm combo, mỗi combo gồm nhiều sản phẩm con
 */
@Entity('combo_products')
export class ComboProduct {
  /**
   * ID sản phẩm combo, liên kết với bảng customer_products
   */
  @PrimaryColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Gi<PERSON> của combo, lưu dạng JSONB để hỗ trợ nhiều loại giá
   */
  @Column({
    name: 'price',
    type: 'jsonb',
    nullable: false,
    comment: 'Giá của combo, lưu dạng JSONB để hỗ trợ nhiều loại giá (ví dụ: theo khu vực, số lượng...)',
  })
  price: ProductPrice;

  /**
   * <PERSON>h sách các sản phẩm con trong combo
   */
  @Column({
    name: 'combo_items',
    type: 'jsonb',
    nullable: false,
    comment: 'Danh sách các sản phẩm con trong combo, lưu dưới dạng JSONB. Mỗi item nên gồm ID sản phẩm, số lượng',
  })
  comboItems: any;

  /**
   * Giới hạn số lượng combo có thể bán
   */
  @Column({
    name: 'max_quantity',
    type: 'integer',
    nullable: true,
    comment: 'Giới hạn số lượng combo có thể bán',
  })
  maxQuantity: number | null;
}
