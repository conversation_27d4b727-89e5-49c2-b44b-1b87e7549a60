import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject, IsString } from 'class-validator';

/**
 * DTO cho việc tạo mới cấu hình máy chủ SMS
 */
export class CreateSmsServerDto {
  @ApiProperty({
    description: 'Tên tích hợp do người dùng đặt (sẽ được lưu vào integration_name)',
    example: 'config FPT SMS',
  })
  @IsNotEmpty({ message: 'Tên tích hợp không được để trống' })
  @IsString({ message: 'Tên tích hợp phải là chuỗi' })
  integration_name: string;

  @ApiProperty({
    description: 'Thông tin cấu hình SMS chi tiết (type sẽ tự động được set là "SMS")',
    type: 'object',
    additionalProperties: true,
    example: {
      integrationName: 'FPT_SMS',
      apiKey: 'your-api-key-here',
      endpoint: 'http://api.fpt.net/api',
      clientId: 'your-client-id'
    },
  })
  @IsNotEmpty({ message: 'Thông tin cấu hình không được để trống' })
  @IsObject({ message: 'Thông tin cấu hình phải là đối tượng' })
  info: Record<string, any>;
}


