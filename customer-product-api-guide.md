# Customer Product CRUD APIs - Hướng dẫn sử dụng

## 📋 **TỔNG QUAN**

API CRUD cho Customer Product đã được triển khai hoàn chỉnh với các tính năng:
- ✅ **CREATE**: Tạo sản phẩm khách hàng mới
- ✅ **READ**: L<PERSON>y danh sách và chi tiết sản phẩm
- ✅ **UPDATE**: Cập nhật thông tin sản phẩm
- ✅ **DELETE**: Xóa mềm sản phẩm (soft delete)
- ✅ **BULK DELETE**: Xó<PERSON> nhiều sản phẩm cùng lúc

## 🔗 **ENDPOINTS**

### **Base URL**: `/api/user/customer-products`

| Method | Endpoint | Mô tả |
|--------|----------|-------|
| POST | `/` | Tạo sản phẩm mới |
| GET | `/` | L<PERSON>y danh sách sản phẩm |
| GET | `/:id` | Lấy chi tiết sản phẩm |
| PUT | `/:id` | Cập nhật sản phẩm |
| DELETE | `/:id` | Xóa sản phẩm |
| DELETE | `/bulk` | Xóa nhiều sản phẩm |

## 🚀 **CHI TIẾT API**

### **1. Tạo sản phẩm khách hàng mới**

```http
POST /api/user/customer-products
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Áo thun nam cao cấp",
  "description": "Áo thun nam chất liệu cotton 100%, thiết kế hiện đại",
  "productType": "PHYSICAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 500000,
    "salePrice": 450000,
    "currency": "VND"
  },
  "tags": ["thời trang", "nam", "cotton"],
  "customFields": [
    {
      "fieldId": "color",
      "fieldValue": "Đỏ"
    },
    {
      "fieldId": "size",
      "fieldValue": "L"
    }
  ]
}
```

**Response (201):**
```json
{
  "success": true,
  "message": "Tạo sản phẩm khách hàng thành công",
  "data": {
    "id": 123,
    "userId": 456,
    "name": "Áo thun nam cao cấp",
    "description": "Áo thun nam chất liệu cotton 100%, thiết kế hiện đại",
    "productType": "PHYSICAL",
    "typePrice": "HAS_PRICE",
    "price": {
      "listPrice": 500000,
      "salePrice": 450000,
      "currency": "VND"
    },
    "tags": ["thời trang", "nam", "cotton"],
    "status": "PENDING",
    "createdAt": 1704067200000,
    "updatedAt": 1704067200000,
    "customFields": {
      "color": "Đỏ",
      "size": "L"
    }
  }
}
```

### **2. Lấy danh sách sản phẩm**

```http
GET /api/user/customer-products?page=1&limit=10&search=áo&status=APPROVED&productType=PHYSICAL
Authorization: Bearer <JWT_TOKEN>
```

**Query Parameters:**
- `page` (optional): Số trang (default: 1)
- `limit` (optional): Số item/trang (default: 10, max: 100)
- `search` (optional): Tìm kiếm theo tên
- `status` (optional): Filter theo trạng thái
- `productType` (optional): Filter theo loại sản phẩm
- `sortBy` (optional): Sắp xếp theo trường (createdAt, updatedAt, name)
- `sortOrder` (optional): Thứ tự sắp xếp (ASC, DESC)

**Response (200):**
```json
{
  "success": true,
  "message": "Lấy danh sách sản phẩm khách hàng thành công",
  "data": {
    "items": [
      {
        "id": 123,
        "userId": 456,
        "name": "Áo thun nam cao cấp",
        "productType": "PHYSICAL",
        "status": "APPROVED",
        "createdAt": 1704067200000
      }
    ],
    "meta": {
      "total": 50,
      "page": 1,
      "limit": 10,
      "totalPages": 5
    }
  }
}
```

### **3. Lấy chi tiết sản phẩm**

```http
GET /api/user/customer-products/123
Authorization: Bearer <JWT_TOKEN>
```

**Response (200):**
```json
{
  "success": true,
  "message": "Lấy chi tiết sản phẩm khách hàng thành công",
  "data": {
    "id": 123,
    "userId": 456,
    "name": "Áo thun nam cao cấp",
    "description": "Áo thun nam chất liệu cotton 100%",
    "productType": "PHYSICAL",
    "typePrice": "HAS_PRICE",
    "price": {
      "listPrice": 500000,
      "salePrice": 450000,
      "currency": "VND"
    },
    "tags": ["thời trang", "nam", "cotton"],
    "status": "APPROVED",
    "createdAt": 1704067200000,
    "updatedAt": 1704067200000,
    "customFields": {
      "color": "Đỏ",
      "size": "L"
    }
  }
}
```

### **4. Cập nhật sản phẩm**

```http
PUT /api/user/customer-products/123
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Áo thun nam cao cấp - Phiên bản mới",
  "price": {
    "listPrice": 550000,
    "salePrice": 500000,
    "currency": "VND"
  },
  "tags": ["thời trang", "nam", "cotton", "premium"],
  "status": "APPROVED"
}
```

### **5. Xóa sản phẩm (soft delete)**

```http
DELETE /api/user/customer-products/123
Authorization: Bearer <JWT_TOKEN>
```

**Response (200):**
```json
{
  "success": true,
  "message": "Xóa sản phẩm khách hàng thành công",
  "data": null
}
```

### **6. Xóa nhiều sản phẩm**

```http
DELETE /api/user/customer-products/bulk
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**Request Body:**
```json
{
  "productIds": [123, 124, 125]
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "Xóa thành công 2/3 sản phẩm",
  "data": {
    "results": [
      {
        "productId": 123,
        "status": "success",
        "message": "Xóa sản phẩm thành công"
      },
      {
        "productId": 124,
        "status": "success",
        "message": "Xóa sản phẩm thành công"
      },
      {
        "productId": 125,
        "status": "error",
        "message": "Không tìm thấy sản phẩm với ID 125"
      }
    ],
    "totalProcessed": 3,
    "successCount": 2,
    "failureCount": 1,
    "message": "Xóa thành công 2/3 sản phẩm"
  }
}
```

## 🔧 **VALIDATION RULES**

### **Tạo sản phẩm:**
- `name`: Bắt buộc, tối đa 500 ký tự
- `productType`: Bắt buộc, enum (PHYSICAL, DIGITAL, EVENT, SERVICE, COMBO)
- `typePrice`: Bắt buộc, enum (HAS_PRICE, STRING_PRICE)
- `description`: Tùy chọn, text
- `price`: Tùy chọn, object
- `tags`: Tùy chọn, array of strings
- `customFields`: Tùy chọn, array of objects

### **Cập nhật sản phẩm:**
- Tất cả fields đều optional
- Validation rules giống như tạo sản phẩm

### **Xóa nhiều sản phẩm:**
- `productIds`: Bắt buộc, array of numbers
- Tối thiểu 1 sản phẩm, tối đa 50 sản phẩm

## 🛡️ **BẢO MẬT**

- **JWT Authentication**: Tất cả API đều yêu cầu JWT token
- **User Ownership**: Chỉ có thể thao tác với sản phẩm của chính mình
- **Soft Delete**: Sản phẩm bị xóa vẫn tồn tại trong DB với status = DELETED

## 📊 **STATUS CODES**

| Code | Mô tả |
|------|-------|
| 200 | Thành công |
| 201 | Tạo thành công |
| 400 | Dữ liệu không hợp lệ |
| 401 | Chưa xác thực |
| 403 | Không có quyền |
| 404 | Không tìm thấy |
| 500 | Lỗi server |

## 🎯 **TÍNH NĂNG ĐẶC BIỆT**

1. **Soft Delete**: Không xóa vật lý, chỉ đổi status
2. **User Isolation**: Mỗi user chỉ thấy sản phẩm của mình
3. **Flexible Pricing**: Hỗ trợ nhiều loại giá (JSONB)
4. **Custom Fields**: Trường tùy chỉnh linh hoạt
5. **Full-text Search**: Tìm kiếm theo tên sản phẩm
6. **Pagination**: Phân trang hiệu quả
7. **Bulk Operations**: Xóa nhiều sản phẩm cùng lúc

API đã sẵn sàng để sử dụng! 🚀
