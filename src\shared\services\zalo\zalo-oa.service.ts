import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloService } from './zalo.service';
import {
  ZaloMessage,
  ZaloOaInfo,
  ZaloTextMessage,
  ZaloImageMessage,
  ZaloFileMessage,
  ZaloTemplateMessage,
  ZaloUserInfo,
} from './zalo.interface';

@Injectable()
export class ZaloOaService {
  private readonly logger = new Logger(ZaloOaService.name);
  private readonly oaApiUrl = 'https://openapi.zalo.me/v3.0/oa';

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Lấy thông tin Official Account
   * @param accessToken Access token của Official Account
   * @returns Thông tin Official Account
   */
  async getOaInfo(accessToken: string): Promise<ZaloOaInfo> {
    try {
      const url = `${this.oaApiUrl}/getoa`;
      return await this.zaloService.get<ZaloOaInfo>(url, accessToken);
    } catch (error) {
      this.logger.error(`Error getting OA info: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin Official Account',
      );
    }
  }

  /**
   * Lấy thông tin người dùng Zalo
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @returns Thông tin người dùng
   */
  async getUserInfo(accessToken: string, userId: string): Promise<ZaloUserInfo> {
    try {
      const url = `${this.oaApiUrl}/getprofile`;
      const params = { user_id: userId };
      return await this.zaloService.get<ZaloUserInfo>(url, accessToken, params);
    } catch (error) {
      this.logger.error(`Error getting user info: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin người dùng Zalo',
      );
    }
  }

  /**
   * Gửi tin nhắn văn bản
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param text Nội dung tin nhắn
   * @returns ID của tin nhắn
   */
  async sendTextMessage(
    accessToken: string,
    userId: string,
    text: string,
  ): Promise<{ message_id: string }> {
    try {
      const url = `${this.oaApiUrl}/message`;
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          text,
        },
      };
      return await this.zaloService.post<{ message_id: string }>(url, accessToken, data);
    } catch (error) {
      this.logger.error(`Error sending text message: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn văn bản',
      );
    }
  }

  /**
   * Gửi tin nhắn hình ảnh
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param imageUrl URL của hình ảnh
   * @param caption Chú thích cho hình ảnh (nếu có)
   * @returns ID của tin nhắn
   */
  async sendImageMessage(
    accessToken: string,
    userId: string,
    imageUrl: string,
    caption?: string,
  ): Promise<{ message_id: string }> {
    try {
      const url = `${this.oaApiUrl}/message`;
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'media',
              elements: [
                {
                  media_type: 'image',
                  url: imageUrl,
                  ...(caption && { caption }),
                },
              ],
            },
          },
        },
      };
      return await this.zaloService.post<{ message_id: string }>(url, accessToken, data);
    } catch (error) {
      this.logger.error(`Error sending image message: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn hình ảnh',
      );
    }
  }

  /**
   * Gửi tin nhắn tệp đính kèm
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param fileUrl URL của tệp đính kèm
   * @param fileName Tên của tệp đính kèm
   * @returns ID của tin nhắn
   */
  async sendFileMessage(
    accessToken: string,
    userId: string,
    fileUrl: string,
    fileName: string,
  ): Promise<{ message_id: string }> {
    try {
      const url = `${this.oaApiUrl}/message`;
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'file',
            payload: {
              url: fileUrl,
              name: fileName,
            },
          },
        },
      };
      return await this.zaloService.post<{ message_id: string }>(url, accessToken, data);
    } catch (error) {
      this.logger.error(`Error sending file message: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn tệp đính kèm',
      );
    }
  }

  /**
   * Gửi tin nhắn template
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param templateId ID của template
   * @param templateData Dữ liệu cho template
   * @returns ID của tin nhắn
   */
  async sendTemplateMessage(
    accessToken: string,
    userId: string,
    templateId: string,
    templateData: Record<string, any>,
  ): Promise<{ message_id: string }> {
    try {
      const url = `${this.oaApiUrl}/message`;
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'list',
              template_id: templateId,
              ...templateData,
            },
          },
        },
      };
      return await this.zaloService.post<{ message_id: string }>(url, accessToken, data);
    } catch (error) {
      this.logger.error(`Error sending template message: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn template',
      );
    }
  }

  /**
   * Gửi tin nhắn dựa vào loại tin nhắn
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param message Tin nhắn cần gửi
   * @returns ID của tin nhắn
   */
  async sendMessage(
    accessToken: string,
    userId: string,
    message: ZaloMessage,
  ): Promise<{ message_id: string }> {
    try {
      switch (message.type) {
        case 'text':
          return await this.sendTextMessage(accessToken, userId, message.text);
        case 'image':
          return await this.sendImageMessage(accessToken, userId, message.url, message.caption);
        case 'file':
          return await this.sendFileMessage(accessToken, userId, message.url, message.name);
        case 'template':
          return await this.sendTemplateMessage(
            accessToken,
            userId,
            message.template_id,
            message.template_data,
          );
        default:
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            'Loại tin nhắn không hợp lệ',
          );
      }
    } catch (error) {
      this.logger.error(`Error sending message: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn',
      );
    }
  }

  /**
   * Thiết lập webhook cho Official Account
   * @param accessToken Access token của Official Account
   * @param webhookUrl URL webhook
   * @returns Kết quả thiết lập
   */
  async setWebhook(accessToken: string, webhookUrl: string): Promise<{ success: boolean }> {
    try {
      const url = `${this.oaApiUrl}/api/subscription/register`;
      const data = {
        webhook_url: webhookUrl,
        event_types: [
          'user_send_text',
          'user_send_image',
          'user_send_file',
          'user_send_sticker',
          'user_send_gif',
          'user_send_audio',
          'user_send_video',
          'user_send_location',
          'user_seen_message',
          'user_submit_info',
          'user_received_message',
          'user_follow',
          'user_unfollow',
        ],
      };
      await this.zaloService.post(url, accessToken, data);
      return { success: true };
    } catch (error) {
      this.logger.error(`Error setting webhook: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi thiết lập webhook',
      );
    }
  }

  /**
   * Lấy thông tin webhook hiện tại
   * @param accessToken Access token của Official Account
   * @returns Thông tin webhook
   */
  async getWebhookInfo(accessToken: string): Promise<{ webhook_url: string; event_types: string[] }> {
    try {
      const url = `${this.oaApiUrl}/api/subscription/info`;
      return await this.zaloService.get(url, accessToken);
    } catch (error) {
      this.logger.error(`Error getting webhook info: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin webhook',
      );
    }
  }
}
